{"job": [{"name": "TmsTrackWithOfficeSiteJob", "type": "com.nsy.api.tms.job.TmsTrackWithOfficeSiteJob", "cron": {"expression": "0 0/5 * * * ?"}, "email-from": "", "email-to": "", "description": "", "param1": "", "param2": ""}, {"name": "TmsTrackWithTrackingMoreJob", "type": "com.nsy.api.tms.job.TmsTrackWithTrackingMoreJob", "cron": {"expression": "0 0/5 * * * ?"}, "email-from": "", "email-to": "", "description": "", "param1": "", "param2": ""}, {"name": "TmsLogisticsTimeReportJob", "type": "com.nsy.api.tms.job.TmsLogisticsTimeReportJob", "cron": {"expression": "0 30 1 * * ?"}, "email-from": "", "email-to": "", "description": "", "param1": "", "param2": ""}, {"name": "TmsTimelinessAnalysisJob", "type": "com.nsy.api.tms.job.TmsTimelinessAnalysisJob", "cron": {"expression": "0 30 2 * * ? 2099"}, "email-from": "", "email-to": "", "description": "", "param1": "", "param2": ""}, {"name": "TmsHuaLeiOrderTrackingNumberJob", "type": "com.nsy.api.tms.job.TmsHuaLeiOrderTrackingNumberJob", "cron": {"expression": "0 0/5 * * * ? 2099"}, "email-from": "", "email-to": "", "description": "", "param1": "", "param2": ""}, {"name": "TmsClearUpRequestLogJob", "type": "com.nsy.api.tms.job.TmsClearUpRequestLogJob", "cron": {"expression": "0 0 5 * * ?"}, "email-from": "", "email-to": "", "description": "", "param1": "", "param2": ""}]}