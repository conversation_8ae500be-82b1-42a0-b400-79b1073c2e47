CREATE TABLE IF NOT EXISTS tms_async_request_queue (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `tid` VARCHAR(100) COMMENT '订单ID',
  `logistics_company` varchar(128) COMMENT '物流公司',
  `logistics_no` varchar(128) COMMENT '包裹编码',
  `key_group` VARCHAR (50) COMMENT '账号信息',
  `logistics_channel_code` VARCHAR (50) COMMENT '物流方式名称',
  `status` TINYINT(1) COMMENT '是否成功:1--成功, -1 ---失败，0--待推送',
  `order_request_content` MEDIUMTEXT COMMENT 'order请求报文',
  `config_map` MEDIUMTEXT COMMENT '配置信息序列化',
  `request_content` MEDIUMTEXT COMMENT '请求报文',
  `response_content` MEDIUMTEXT COMMENT '响应报文',
  `retry_count` int(11) DEFAULT 0 COMMENT '重推次数',
  `delete_flag` TINYINT(1) COMMENT '是否删除:1--删除, 0--未删除',
  `create_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` VARCHAR (45) COMMENT '创建者',
  `update_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` VARCHAR (45) COMMENT '更新者'
) ENGINE = INNODB COMMENT '异步请求日志表' ;

alter table tms_async_request_queue add index ix_logistics_company(logistics_company);
alter table tms_async_request_queue add index ix_key_group(key_group);
alter table tms_async_request_queue add index ix_logistics_channel_code(logistics_channel_code);
alter table tms_async_request_queue add index ix_status(status);
alter table tms_async_request_queue add index ix_create_date(create_date);
alter table tms_async_request_queue add index ix_retry_count(retry_count);
alter table tms_async_request_queue add index ix_delete_flag(delete_flag);