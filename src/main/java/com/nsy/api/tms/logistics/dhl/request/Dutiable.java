package com.nsy.api.tms.logistics.dhl.request;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "Dutiable")
public class Dutiable {
    @XmlElement(name = "DeclaredValue")
    private Double declaredValue;
    @XmlElement(name = "DeclaredCurrency")
    private String declaredCurrency;
    @XmlElement(name = "ShipperEIN")
    private String shipperEIN;
    @XmlElement(name = "ConsigneeEIN")
    private String consigneeEIN;
    @XmlElement(name = "TermsOfTrade")
    private String termsOfTrade;

    public Double getDeclaredValue() {
        return declaredValue;
    }

    public void setDeclaredValue(Double declaredValue) {
        this.declaredValue = declaredValue;
    }

    public String getDeclaredCurrency() {
        return declaredCurrency;
    }

    public void setDeclaredCurrency(String declaredCurrency) {
        this.declaredCurrency = declaredCurrency;
    }

    public String getShipperEIN() {
        return shipperEIN;
    }

    public void setShipperEIN(String shipperEIN) {
        this.shipperEIN = shipperEIN;
    }

    public String getConsigneeEIN() {
        return consigneeEIN;
    }

    public void setConsigneeEIN(String consigneeEIN) {
        this.consigneeEIN = consigneeEIN;
    }

    public String getTermsOfTrade() {
        return termsOfTrade;
    }

    public void setTermsOfTrade(String termsOfTrade) {
        this.termsOfTrade = termsOfTrade;
    }

}
