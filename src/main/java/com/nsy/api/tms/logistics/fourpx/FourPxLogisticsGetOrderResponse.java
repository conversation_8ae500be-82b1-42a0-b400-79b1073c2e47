package com.nsy.api.tms.logistics.fourpx;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public class FourPxLogisticsGetOrderResponse extends FourPxLogisticsBaseResponse {
    @JsonProperty(value = "data")
    private List<Data> data;

    public List<Data> getData() {
        return data;
    }

    public void setData(List<Data> data) {
        this.data = data;
    }

    public static class Data {

        @JsonProperty(value = "consignment_info")
        private ConsignmentInfo dsConsignmentNo;

        public ConsignmentInfo getDsConsignmentNo() {
            return dsConsignmentNo;
        }

        public void setDsConsignmentNo(ConsignmentInfo dsConsignmentNo) {
            this.dsConsignmentNo = dsConsignmentNo;
        }
    }

    public static class ConsignmentInfo {
        @JsonProperty(value = "4px_tracking_no")
        private String trackingNumber;

        @JsonProperty(value = "logistics_channel_no")
        private String logisticsChannelNo;

        public String getTrackingNumber() {
            return trackingNumber;
        }

        public void setTrackingNumber(String trackingNumber) {
            this.trackingNumber = trackingNumber;
        }

        public String getLogisticsChannelNo() {
            return logisticsChannelNo;
        }

        public void setLogisticsChannelNo(String logisticsChannelNo) {
            this.logisticsChannelNo = logisticsChannelNo;
        }
    }

}
