package com.nsy.api.tms.logistics.tnt.request.ship;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/21 16:23
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "PRINT")
public class Print {
    @XmlElement(name = "LABEL")
    private Label label;

    public Label getLabel() {
        return label;
    }

    public void setLabel(Label label) {
        this.label = label;
    }

}
