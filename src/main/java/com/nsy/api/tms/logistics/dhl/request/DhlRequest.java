package com.nsy.api.tms.logistics.dhl.request;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Request")
public class DhlRequest {
    @XmlElement(name = "ServiceHeader")
    private ServiceHeader serviceHeader;
    @XmlElement(name = "MetaData")
    private MetaData metaData;

    public ServiceHeader getServiceHeader() {
        return serviceHeader;
    }

    public void setServiceHeader(ServiceHeader serviceHeader) {
        this.serviceHeader = serviceHeader;
    }

    public MetaData getMetaData() {
        return metaData;
    }

    public void setMetaData(MetaData metaData) {
        this.metaData = metaData;
    }
}
