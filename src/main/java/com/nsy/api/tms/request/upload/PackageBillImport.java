package com.nsy.api.tms.request.upload;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

public class PackageBillImport {

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 业务日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date businessDate;

    private String realWeightStr;

    private String realBasicFreightStr;

    private String realFuelPriceStr;


    private String realSurchargePriceStr;


    private String realRemoteAreaFeeStr;


    private String realTaxStr;


    private String realServiceFeeStr;


    private String realRegistrationPriceStr;


    private String realOtherFreightStr;

    /**
     * 实际物流重量
     */
    @ApiModelProperty("实际物流重量")
    @NotNull(message = "实际物流重量")
    private BigDecimal realWeight;

    /**
     * 实际物流重量
     */
    @ApiModelProperty("实际基础物流费用")
    @NotNull(message = "实际基础物流费用")
    private BigDecimal realBasicFreight;

    @ApiModelProperty("实际其他费用(元)")
    private BigDecimal realOtherFreight;

    /**
     * 实际燃油费(元)
     */
    @ApiModelProperty("实际燃油费(元)")
    private BigDecimal realFuelPrice;

    /**
     * 实际附加费(元)
     */
    @ApiModelProperty("实际附加费(元)")
    private BigDecimal realSurchargePrice;

    /**
     * 实际偏远费(元)
     */
    @ApiModelProperty("实际偏远费(元)")
    private BigDecimal realRemoteAreaFee;

    /**
     * 实际关税(元)
     */
    @ApiModelProperty("实际关税(元)")
    private BigDecimal realTax;

    /**
     * 实际手续费(元)
     */
    @ApiModelProperty("实际手续费(元)")
    private BigDecimal realServiceFee;

    /**
     * 实际挂号费(元)
     */
    @ApiModelProperty("实际挂号费(元)")
    private BigDecimal realRegistrationPrice;

    /**
     * 错误信息
     */
    private String errorMsg;

    public String getRealWeightStr() {
        return realWeightStr;
    }

    public void setRealWeightStr(String realWeightStr) {
        this.realWeightStr = realWeightStr;
    }

    public String getRealBasicFreightStr() {
        return realBasicFreightStr;
    }

    public void setRealBasicFreightStr(String realBasicFreightStr) {
        this.realBasicFreightStr = realBasicFreightStr;
    }

    public String getRealFuelPriceStr() {
        return realFuelPriceStr;
    }

    public void setRealFuelPriceStr(String realFuelPriceStr) {
        this.realFuelPriceStr = realFuelPriceStr;
    }

    public String getRealSurchargePriceStr() {
        return realSurchargePriceStr;
    }

    public void setRealSurchargePriceStr(String realSurchargePriceStr) {
        this.realSurchargePriceStr = realSurchargePriceStr;
    }

    public String getRealRemoteAreaFeeStr() {
        return realRemoteAreaFeeStr;
    }

    public void setRealRemoteAreaFeeStr(String realRemoteAreaFeeStr) {
        this.realRemoteAreaFeeStr = realRemoteAreaFeeStr;
    }

    public String getRealTaxStr() {
        return realTaxStr;
    }

    public void setRealTaxStr(String realTaxStr) {
        this.realTaxStr = realTaxStr;
    }

    public String getRealServiceFeeStr() {
        return realServiceFeeStr;
    }

    public void setRealServiceFeeStr(String realServiceFeeStr) {
        this.realServiceFeeStr = realServiceFeeStr;
    }

    public String getRealRegistrationPriceStr() {
        return realRegistrationPriceStr;
    }

    public void setRealRegistrationPriceStr(String realRegistrationPriceStr) {
        this.realRegistrationPriceStr = realRegistrationPriceStr;
    }

    public String getRealOtherFreightStr() {
        return realOtherFreightStr;
    }

    public void setRealOtherFreightStr(String realOtherFreightStr) {
        this.realOtherFreightStr = realOtherFreightStr;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public BigDecimal getRealWeight() {
        return realWeight;
    }

    public void setRealWeight(BigDecimal realWeight) {
        this.realWeight = realWeight;
    }

    public BigDecimal getRealBasicFreight() {
        return realBasicFreight;
    }

    public void setRealBasicFreight(BigDecimal realBasicFreight) {
        this.realBasicFreight = realBasicFreight;
    }

    public BigDecimal getRealOtherFreight() {
        return realOtherFreight;
    }

    public void setRealOtherFreight(BigDecimal realOtherFreight) {
        this.realOtherFreight = realOtherFreight;
    }

    public BigDecimal getRealFuelPrice() {
        return realFuelPrice;
    }

    public void setRealFuelPrice(BigDecimal realFuelPrice) {
        this.realFuelPrice = realFuelPrice;
    }

    public BigDecimal getRealSurchargePrice() {
        return realSurchargePrice;
    }

    public void setRealSurchargePrice(BigDecimal realSurchargePrice) {
        this.realSurchargePrice = realSurchargePrice;
    }

    public BigDecimal getRealRemoteAreaFee() {
        return realRemoteAreaFee;
    }

    public void setRealRemoteAreaFee(BigDecimal realRemoteAreaFee) {
        this.realRemoteAreaFee = realRemoteAreaFee;
    }

    public BigDecimal getRealTax() {
        return realTax;
    }

    public void setRealTax(BigDecimal realTax) {
        this.realTax = realTax;
    }

    public BigDecimal getRealServiceFee() {
        return realServiceFee;
    }

    public void setRealServiceFee(BigDecimal realServiceFee) {
        this.realServiceFee = realServiceFee;
    }

    public BigDecimal getRealRegistrationPrice() {
        return realRegistrationPrice;
    }

    public void setRealRegistrationPrice(BigDecimal realRegistrationPrice) {
        this.realRegistrationPrice = realRegistrationPrice;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
