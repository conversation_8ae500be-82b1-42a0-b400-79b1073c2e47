package com.nsy.api.tms.dao.entity.freight;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.api.tms.dao.entity.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 渠道价格账单(VpsChannelFreightBill)实体类
 *
 * <AUTHOR>
 * @since 2023-09-04 17:05:25
 */
@Entity
@Table(name = "vps_channel_freight_bill")
@TableName("vps_channel_freight_bill")
public class VpsChannelFreightBillEntity extends BaseMpEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer companyId;

    /**
     * 账单名称
     */
    private String billName;

    /**
     * 操作IP
     */
    private String billFileUrl;

    private String status;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getBillName() {
        return billName;
    }

    public void setBillName(String billName) {
        this.billName = billName;
    }

    public String getBillFileUrl() {
        return billFileUrl;
    }

    public void setBillFileUrl(String billFileUrl) {
        this.billFileUrl = billFileUrl;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }


}

