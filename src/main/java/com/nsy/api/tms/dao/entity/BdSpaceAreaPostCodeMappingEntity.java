package com.nsy.api.tms.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Version;

/**
 * 仓库地区邮编映射实体类
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Entity
@Table(name = "`bd_space_area_post_code_mapping`")
@TableName("bd_space_area_post_code_mapping")
public class BdSpaceAreaPostCodeMappingEntity extends BaseDataManipulationEntity {

    /** 仓库名称 */
    @Column(name = "`space_id`")
    private Integer spaceId;

    /** 仓库名称 */
    @Column(name = "`space_name`")
    private String spaceName;

    /** 邮编 */
    @Column(name = "`post_code`")
    private String postCode;

    /** 地区名称 */
    @Column(name = "`area_name`")
    private String areaName;

    /** 版本号 */
    @Column(name = "`version`")
    @Version
    private Long version;

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }
}
