package com.nsy.api.tms.service.freight;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.constants.CountryCodeConstant;
import com.nsy.api.tms.constants.PackageBillConstant;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsCompanyEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.freight.LogisticsFuelRateEntity;
import com.nsy.api.tms.dao.entity.freight.PackageBillEntity;
import com.nsy.api.tms.dao.entity.freight.StockoutShipmentEntity;
import com.nsy.api.tms.dao.entity.freight.StockoutShipmentItemEntity;
import com.nsy.api.tms.dao.entity.freight.VpsChannelCountryFreightEntity;
import com.nsy.api.tms.domain.LogisticsInfoDto;
import com.nsy.api.tms.domain.MatchPriceDto;
import com.nsy.api.tms.domain.PriceCalculateResultDto;
import com.nsy.api.tms.domain.TmsLogisticsAccount;
import com.nsy.api.tms.enumeration.BillingTypeChannelEnum;
import com.nsy.api.tms.enumeration.ChargeWeightTypeEnum;
import com.nsy.api.tms.enumeration.LogisticsDifferenceCauseEnum;
import com.nsy.api.tms.enumeration.LogisticsDifferenceTypeEnum;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.enumeration.LogisticsTypeEnum;
import com.nsy.api.tms.enumeration.PackageBillFinancialPushStatusEnum;
import com.nsy.api.tms.enumeration.PackageBillReviewResultEnum;
import com.nsy.api.tms.enumeration.PackageBillStatusEnum;
import com.nsy.api.tms.external.fms.FmsApiService;
import com.nsy.api.tms.external.wms.WmsApiService;
import com.nsy.api.tms.external.wms.response.LogisticsDocumentsBaseRequestInfo;
import com.nsy.api.tms.external.wms.response.ProductSpecInfo;
import com.nsy.api.tms.mapper.freight.PackageBillMapper;
import com.nsy.api.tms.repository.TmsLogisticsChannelConfigRepository;
import com.nsy.api.tms.repository.TmsLogisticsCompanyRepository;
import com.nsy.api.tms.request.freight.PackageBillPageRequest;
import com.nsy.api.tms.request.freight.PackageBillUpdateBatchRequest;
import com.nsy.api.tms.request.freight.PackageBillUpdateRequest;
import com.nsy.api.tms.request.upload.PackageBillImport;
import com.nsy.api.tms.request.upload.UploadRequest;
import com.nsy.api.tms.response.PageResponse;
import com.nsy.api.tms.response.freight.PackageBillResponse;
import com.nsy.api.tms.service.PackageNewService;
import com.nsy.api.tms.service.TmsExtendedAreaSurchargeService;
import com.nsy.api.tms.service.TmsLogisticsAccountService;
import com.nsy.api.tms.service.TmsLogisticsChannelConfigService;
import com.nsy.api.tms.service.privilege.AccessControlService;
import com.nsy.api.tms.utils.JsonMapper;
import com.nsy.permission.IgnoreLocationContext;
import com.nsy.permission.annatation.Permission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物流包裹账单(PackageBill)服务层
 *
 * <AUTHOR>
 * @since 2023-09-14 14:54:12
 */
@Service
public class PackageBillService extends ServiceImpl<PackageBillMapper, PackageBillEntity> {

    private static final Logger LOGGER = LoggerFactory.getLogger(PackageBillService.class);

    @Resource
    AccessControlService accessControlService;
    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    StockoutShipmentService shipmentService;
    @Autowired
    TmsLogisticsAccountService accountService;
    @Autowired
    PackageNewService packageNewService;
    @Autowired
    TmsLogisticsChannelConfigService channelConfigService;
    @Autowired
    TmsLogisticsChannelConfigRepository channelConfigRepository;
    @Autowired
    TmsLogisticsChannelConfigRepository logisticsChannelConfigRepository;
    @Autowired
    TmsLogisticsCompanyRepository logisticsCompanyRepository;
    @Autowired
    LogisticsFuelRateService fuelRateService;
    @Autowired
    VpsChannelFreightService channelFreightService;
    @Autowired
    VpsChannelCountryFreightService countryFreightService;
    @Autowired
    BdChannelCountryAreaMappingService countryAreaMappingService;
    @Autowired
    PackageBillPermissionService packageBillPermissionService;
    @Resource
    WmsApiService wmsApiService;
    @Autowired
    TmsExtendedAreaSurchargeService areaSurchargeService;
    @Autowired
    FmsApiService fmsApiService;

    /**
     * 分页查询
     */
    @Transactional
    public PageResponse<PackageBillResponse> queryByPage(PackageBillPageRequest request) {
        PageResponse<PackageBillResponse> pageResponse = new PageResponse<>();
        orderNoSearch(request);
        IPage<PackageBillEntity> iPage = packageBillPermissionService.pageSearchList(request);
        if (CollectionUtils.isEmpty(iPage.getRecords())) {
            return pageResponse;
        }
        Set<Integer> logisticsCompanyIds = new HashSet<>();
        Set<Integer> channelIds = new HashSet<>();
        List<String> logisticsNos = new ArrayList<>();
        iPage.getRecords().forEach(entity -> {
            logisticsCompanyIds.add(entity.getLogisticsCompanyId());
            channelIds.add(entity.getChannelId());
            logisticsNos.add(entity.getLogisticsNo());
        });
        Map<Integer, TmsLogisticsCompanyEntity> logisticsCompanyMap = logisticsCompanyRepository.findAllById(logisticsCompanyIds).stream().collect(Collectors.toMap(TmsLogisticsCompanyEntity::getId, Function.identity()));
        Map<Integer, TmsLogisticsChannelConfigEntity> channelConfigMap = channelConfigRepository.findAllById(channelIds).stream().collect(Collectors.toMap(TmsLogisticsChannelConfigEntity::getId, Function.identity()));
        List<LogisticsInfoDto> logisticsInfoList = shipmentItemService.countQtyByLogisticsNo(logisticsNos);
        Map<String, Integer> logisticsInfoMap = logisticsInfoList.stream().collect(Collectors.toMap(LogisticsInfoDto::getLogisticsNo, LogisticsInfoDto::getTotalQty));
        List<PackageBillResponse> list = iPage.getRecords().stream().map(entity -> {
            PackageBillResponse resp = new PackageBillResponse();
            if (request.isVerify()) {
                verify(Collections.singletonList(entity.getId()), "批量重新核对");
                PackageBillEntity newEntity = getById(entity.getId());
                BeanUtils.copyProperties(newEntity, resp);
            } else {
                BeanUtils.copyProperties(entity, resp);
            }
            TmsLogisticsCompanyEntity logisticsCompanyEntity = logisticsCompanyMap.getOrDefault(entity.getLogisticsCompanyId(), new TmsLogisticsCompanyEntity());
            resp.setLogisticsCompanyName(logisticsCompanyEntity.getLogisticsCompany());
            TmsLogisticsChannelConfigEntity channelConfigEntity = channelConfigMap.getOrDefault(entity.getChannelId(), new TmsLogisticsChannelConfigEntity());
            resp.setChannelName(channelConfigEntity.getLogisticsChannelName());
            resp.setStatusCn(PackageBillStatusEnum.getDescByName(resp.getStatus()));
            if (entity.getReviewResult() != null) {
                resp.setReviewResultCn(PackageBillReviewResultEnum.resolveByValue(entity.getReviewResult()).getDesc());
            }
            resp.setFinancialPushStatusCn(PackageBillFinancialPushStatusEnum.resolveByValue(entity.getFinancialPushStatus()).getDesc());
            resp.setCountryCodeCn(CountryCodeConstant.getCountryCodeMap().get(resp.getCountryCode()));
            resp.setDifferenceTypeCn(LogisticsDifferenceTypeEnum.getEnumByCode(resp.getDifferenceType()).getDesc());
            resp.setDifferenceCauseCn(LogisticsDifferenceCauseEnum.getDescByCode(resp.getDifferenceCause()));
            resp.setShipmentQty(logisticsInfoMap.getOrDefault(resp.getLogisticsNo(), 0));
            return resp;
        }).collect(Collectors.toList());
        pageResponse.setContent(list);
        pageResponse.setTotalCount(iPage.getTotal());
        return pageResponse;
    }

    private void orderNoSearch(PackageBillPageRequest request) {
        if (CollectionUtils.isEmpty(request.getOrderNos())) {
            return;
        }
        LambdaQueryWrapper<StockoutShipmentItemEntity> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.in(StockoutShipmentItemEntity::getOrderNo, request.getOrderNos());
        wrapper1.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        List<StockoutShipmentItemEntity> list = shipmentItemService.list(wrapper1);
        if (CollectionUtils.isEmpty(list)) {
            request.getIds().clear();
            request.getIds().add(0);
            return;
        }
        List<String> logisticsNos = shipmentService.listByIds(list.stream()
                        .map(StockoutShipmentItemEntity::getShipmentId).collect(Collectors.toList())).stream()
                .map(StockoutShipmentEntity::getLogisticsNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(logisticsNos)) {
            request.getIds().clear();
            request.getIds().add(0);
            return;
        }
        request.getLogisticsNos().addAll(logisticsNos);
    }

    public PackageBillResponse statistics(PackageBillPageRequest pageRequest) {
        orderNoSearch(pageRequest);
        return this.getBaseMapper().statisticsList(pageRequest);
    }

    public PackageBillEntity getByLogisticsNo(String logisticsNo) {
        LambdaQueryWrapper<PackageBillEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PackageBillEntity::getLogisticsNo, logisticsNo);
        wrapper.orderByDesc(PackageBillEntity::getId);
        wrapper.last("limit 1");
        return getOne(wrapper);
    }

    /**
     * 获取账单数据，带权限配置
     *
     * @param logisticsNo
     * @return
     */
    @Permission
    public PackageBillEntity getByLogisticsNoWithPermission(String logisticsNo) {
        return this.getBaseMapper().getByLogisticsNo(logisticsNo);
    }


    //初始化账单
    public void initBill(String logisticsNo) {
        TmsPackageEntity packageEntity = packageNewService.findPackageByLogisticsNo(logisticsNo);
        if (packageEntity == null) {
            LOGGER.error("{}找不到包裹，无法创建账单！", logisticsNo);
            return;
        }
        PackageBillEntity billEntity = packageBillPermissionService.getPackageBillWithPermission(logisticsNo);
        List<StockoutShipmentEntity> shipmentList = shipmentService.findByLogisticsNo(logisticsNo);
        // 找出对应的装箱清单
        if (CollectionUtils.isEmpty(shipmentList)) {
            if (billEntity != null) {
                LOGGER.error("无装箱清单，删除账单：{}", JsonMapper.toJson(billEntity));
                removeById(billEntity.getId());
            } else {
                LOGGER.error("{}找不到装箱清单，无法创建账单！", logisticsNo);
            }
            return;
        }
        PackageBillEntity entity = new PackageBillEntity();
        if (billEntity != null)
            entity.setId(billEntity.getId());
        entity.setLogisticsNo(logisticsNo);
        if (StrUtil.equalsAnyIgnoreCase(packageEntity.getLogisticsType(), LogisticsTypeEnum.INTERNATIONAL_EXPRESS.getName())) {
            if (packageEntity.getLogisticsAccountId() == null) {
                LOGGER.error("{}国际快递且没有账号，无法创建账单！", logisticsNo);
                return;
            }
            TmsLogisticsAccount logisticsAccountDetail = accountService.getLogisticsAccountDetail(packageEntity.getLogisticsAccountId());
            entity.setAccount(logisticsAccountDetail.getLogisticsAccount());
        } else {
            entity.setAccount(null);
        }
        setBillValue(packageEntity, entity);
        TmsLogisticsChannelConfigEntity channelConfig = logisticsChannelConfigRepository.findByLogisticsCompanyAndLogisticsChannelCode(packageEntity.getLogisticsCompany(), packageEntity.getLogisticsChannelCode());
        if (channelConfig == null) {
            LOGGER.error("{}找不到物流渠道，无法创建账单！", logisticsNo);
            return;
        }
        entity.setChannelId(channelConfig.getId());
        TmsLogisticsCompanyEntity logisticsCompany = logisticsCompanyRepository.findByLogisticsCompany(channelConfig.getLogisticsCompany());
        entity.setLogisticsCompanyId(logisticsCompany.getId());
        entity.setLocation(logisticsCompany.getLocation());
        // 初始化重量、体积重、发货时间
        setBillByShipment(entity, shipmentList, logisticsCompany);
        saveOrUpdate(entity);
    }

    private void setBillValue(TmsPackageEntity packageEntity, PackageBillEntity entity) {
        entity.setCreateBy("发货自动创建");
        entity.setStatus(PackageBillStatusEnum.INIT.name());
        entity.setStoreId(packageEntity.getStoreId());
        entity.setStoreName(packageEntity.getStoreName());
        entity.setBusinessType(packageEntity.getDeptName());
        entity.setPostCode(packageEntity.getPostCode());
        entity.setCountryCode(packageEntity.getCountryCode());
        entity.setDifferenceType(LogisticsDifferenceTypeEnum.WAIT_START.getCode());
    }

    // 初始化重量、体积重、发货时间、偏远费、关税、手续费
    private void setBillByShipment(PackageBillEntity entity, List<StockoutShipmentEntity> shipmentList, TmsLogisticsCompanyEntity logisticsCompany) {
        entity.setShipmentWeight(BigDecimal.ZERO);
        entity.setShipmentVolumeWeight(BigDecimal.ZERO);
        List<Integer> shipmentIds = new ArrayList<>();
        shipmentList.forEach(shipment -> {
            entity.setShipmentWeight(entity.getShipmentWeight().add(shipment.getWeight() == null ? BigDecimal.ZERO : shipment.getWeight()));
            if (logisticsCompany.getVolumetricWeightFactor() != null) {
                String[] boxSizeArray = (shipment.getBoxSize() == null ? "" : shipment.getBoxSize()).split("\\*");
                if (boxSizeArray.length == 3) {
                    int length = Integer.parseInt(boxSizeArray[0]);
                    int width = Integer.parseInt(boxSizeArray[1]);
                    int height = Integer.parseInt(boxSizeArray[2]);
                    BigDecimal volume = BigDecimal.valueOf((long) length * width * height).divide(BigDecimal.valueOf(logisticsCompany.getVolumetricWeightFactor()), 3, BigDecimal.ROUND_HALF_UP);
                    entity.setShipmentVolumeWeight(entity.getShipmentVolumeWeight().add(volume));
                }
            } else {
                entity.setShipmentVolumeWeight(entity.getShipmentVolumeWeight().add(shipment.getVolumeWeight() == null ? BigDecimal.ZERO : shipment.getVolumeWeight()));
            }
            if (shipment.getDeliveryDate() != null) {
                if (entity.getDeliveryDate() == null) {
                    entity.setDeliveryDate(shipment.getDeliveryDate());
                } else if (shipment.getDeliveryDate().getTime() >= entity.getDeliveryDate().getTime()) {
                    entity.setDeliveryDate(shipment.getDeliveryDate());
                }
            }
            shipmentIds.add(shipment.getShipmentId());
        });
        if (!CollectionUtils.isEmpty(shipmentIds)) {
            List<StockoutShipmentItemEntity> itemEntityList = shipmentItemService.getByShipmentIds(shipmentIds);
            entity.setOrderNos(StrUtil.maxLength(itemEntityList.stream().map(StockoutShipmentItemEntity::getOrderNo)
                    .distinct().collect(Collectors.joining(",")), 252));
            // 计算商品重量
            List<String> skuList = itemEntityList.stream().map(StockoutShipmentItemEntity::getSku)
                    .distinct().collect(Collectors.toList());
            Map<String, ProductSpecInfo> skuInfoMap = wmsApiService.productSkuInfoList(skuList).getSkuInfoMap();
            entity.setProductWeight(BigDecimal.ZERO);
            itemEntityList.forEach(shipmentItem -> {
                if (entity.getProductWeight() == null) {
                    return;
                }
                ProductSpecInfo productSpec = skuInfoMap.getOrDefault(shipmentItem.getSku(), new ProductSpecInfo());
                if (productSpec.getActualWeight() == null) {
                    entity.setProductWeight(null);
                    return;
                }
                entity.setProductWeight(NumberUtil.add(entity.getProductWeight(), NumberUtil.mul(productSpec.getActualWeight(), shipmentItem.getQty())));
            });
        }
        BigDecimal bigDecimal = ChargeWeightTypeEnum.calculateByName(logisticsCompany.getChargeWeightType(), entity.getShipmentWeight());
        entity.setChargeWeight(bigDecimal == null ? BigDecimal.ZERO : bigDecimal);
        if (StrUtil.equalsIgnoreCase(logisticsCompany.getLogisticsType(), LogisticsTypeEnum.INTERNATIONAL_EXPRESS.name())) {
            BigDecimal volumeWeight = NumberUtil.mul(entity.getShipmentVolumeWeight(), new BigDecimal("1.15"));
            if (NumberUtil.isLess(entity.getShipmentWeight(), volumeWeight)) {
                entity.setChargeWeight(ChargeWeightTypeEnum.calculateByName(logisticsCompany.getChargeWeightType(), volumeWeight));
            }
            entity.setRealChargeWeight(ChargeWeightTypeEnum.calculateByName(logisticsCompany.getChargeWeightType(), entity.getRealWeight()));
        }
    }

    // 计算偏远费 --国际快递计算偏远费
    private static void calculateRemoteAreaFee(MatchPriceDto dto, PriceCalculateResultDto resultDto) {
        if (StrUtil.isBlank(resultDto.getLevel())) {
            // 没有偏远等级，不需要计算偏远费用
            LOGGER.info("账单:{}没有偏远等级，不需要计算偏远费用", dto.getLogisticsNo());
            return;
        }
        // 国际快递
        if (StrUtil.equalsIgnoreCase(dto.getLogisticsCompanyEntity().getLogisticsType(), LogisticsTypeEnum.INTERNATIONAL_EXPRESS.name())) {
            // 计算国际快递偏远费用
            buildInternationalExpressRemoteAreaFee(dto, resultDto);
            LOGGER.info("账单:{}国际快递计算偏远费用成功！费用：{}", dto.getLogisticsNo(), resultDto.getEstimateRemoteAreaFee());
            return;
        }
        // 货代 且 不是FBA订单
        if (StrUtil.equalsIgnoreCase(dto.getLogisticsCompanyEntity().getLogisticsType(), LogisticsTypeEnum.FREIGHT_FORWARDER.name()) && StrUtil.isBlank(dto.getDestinationFulfillmentCenterId())) {
            // 偏远费 = 计费重量 * 3 元
            resultDto.setEstimateRemoteAreaFee(new BigDecimal(3).multiply(dto.getShipmentWeight()));
            LOGGER.info("账单:{}货代计算偏远费用成功！费用：{}", dto.getLogisticsNo(), resultDto.getEstimateRemoteAreaFee());
        }
    }

    private static void buildInternationalExpressRemoteAreaFee(MatchPriceDto dto, PriceCalculateResultDto resultDto) {
        if (StrUtil.equalsAnyIgnoreCase(dto.getLogisticsCompanyEntity().getLogisticsMethod(), LogisticsMethodEnum.FEDEX_V2.getLogisticsMethod(), LogisticsMethodEnum.FEDEX.getLogisticsMethod())) {
            // fedex 计算如下：
            // A偏远：23元/票
            // B偏远：168元/票 或者 3.5元/公斤 计算结果取较大值
            // C偏远：212元/票 或者 4.6元/公斤 计算结果取较大值
            if (StrUtil.equals(resultDto.getLevel(), "A")) {
                resultDto.setEstimateRemoteAreaFee(new BigDecimal("23"));
            } else if (StrUtil.equals(resultDto.getLevel(), "B")) {
                resultDto.setEstimateRemoteAreaFee(NumberUtil.max(new BigDecimal("168"), NumberUtil.mul(dto.getShipmentWeight(), new BigDecimal("3.5"))));
            } else if (StrUtil.equals(resultDto.getLevel(), "C")) {
                resultDto.setEstimateRemoteAreaFee(NumberUtil.max(new BigDecimal("212"), NumberUtil.mul(dto.getShipmentWeight(), new BigDecimal("4.6"))));
            }
        } else if (StrUtil.equalsAnyIgnoreCase(dto.getLogisticsCompanyEntity().getLogisticsMethod(), LogisticsMethodEnum.DHL.getLogisticsMethod())) {
            // DHL偏远计算公式单票168或者每公斤3.7（以大取大
            resultDto.setEstimateRemoteAreaFee(NumberUtil.max(new BigDecimal("168"), NumberUtil.mul(dto.getShipmentWeight(), new BigDecimal("3.7"))));
        } else {
            // UPS不需要计算
            resultDto.setEstimateRemoteAreaFee(BigDecimal.ZERO);
        }
    }

    // 初步计算价格
    public void evaluateBill(String logisticsNo) {
        PackageBillEntity billEntity = packageBillPermissionService.getPackageBillWithPermission(logisticsNo);
        if (billEntity == null) {
            LOGGER.error("{}找不到账单，无法预算价格！", logisticsNo);
            return;
        }
        List<String> errorList = new ArrayList<>();
        // 计算[预估基础物流费用]
        Date deliveryDate = billEntity.getDeliveryDate();
        TmsLogisticsCompanyEntity logisticsCompanyEntity = logisticsCompanyRepository.findById(billEntity.getLogisticsCompanyId())
                .orElseThrow(() -> new BusinessServiceException(String.format("物流公司【%s】不存在，账单自动预算价格失败！", billEntity.getLogisticsCompanyId())));
        String account = null;
        BigDecimal fuelRate = BigDecimal.ZERO;
        MatchPriceDto dto = new MatchPriceDto();
        dto.setLogisticsCompanyEntity(logisticsCompanyEntity);
        if (StrUtil.equalsAnyIgnoreCase(logisticsCompanyEntity.getLogisticsType(), LogisticsTypeEnum.INTERNATIONAL_EXPRESS.name())) {
            // 查找费率
            LogisticsFuelRateEntity fuelRateEntity = fuelRateService.getByDateAndCompanyId(billEntity.getLogisticsCompanyId(), deliveryDate);
            if (fuelRateEntity == null) {
                errorList.add("费率未维护！");
            } else {
                fuelRate = fuelRateEntity.getFuelRate().divide(new BigDecimal("100"), 4, BigDecimal.ROUND_HALF_UP);
            }
            account = billEntity.getAccount();
        }
        dto.setDeliveryDate(billEntity.getDeliveryDate());
        dto.setLogisticsNo(billEntity.getLogisticsNo());
        // 查找运价
        List<VpsChannelCountryFreightEntity> countryFreightEntities = channelFreightService.matchByChannelIdAndDateAndAccount(billEntity.getChannelId(), billEntity.getCountryCode(), billEntity.getDeliveryDate(), account);
        if (billEntity.getChargeWeight() == null) {
            errorList.add("仓库重量未维护！");
        } else {
            dto.setPostCode(billEntity.getPostCode());
            dto.setShipmentWeight(billEntity.getChargeWeight());
            dto.setFuelRate(fuelRate);
            PriceCalculateResultDto freight = getFreight(dto, countryFreightEntities);
            if (StrUtil.isNotBlank(freight.getErrorStr())) {
                errorList.add(freight.getErrorStr());
            } else {
                // 没有错误，获取到价格
                billEntity.setEstimateTotalPrice(freight.getTotalPrice());
                billEntity.setEstimateBasicPrice(freight.getBasicPrice());
                billEntity.setEstimateRemoteAreaFee(freight.getEstimateRemoteAreaFee());
                billEntity.setEstimateTax(freight.getEstimateTax());
                billEntity.setEstimateServiceFee(freight.getEstimateServiceFee());
                billEntity.setEstimateFuelPrice(freight.getFuelPrice());
                billEntity.setEstimateSurchargePrice(freight.getSurchargePrice());
                billEntity.setEstimateRegistrationPrice(freight.getRegistrationPrice());
                billEntity.setCountryFreightId(freight.getTargetCountryFreightId());
                billEntity.setExtendAreaLevel(freight.getLevel());
            }
        }
        if (!CollectionUtils.isEmpty(errorList)) {
            billEntity.setSystemRemark(String.join("且", errorList));
            billEntity.setStatus(PackageBillStatusEnum.EXCEPTION.name());
            updateByIdIgnoreTenant(billEntity);
            return;
        }
        billEntity.setStatus(PackageBillStatusEnum.INIT.name());
        billEntity.setSystemRemark("");
        updateByIdIgnoreTenant(billEntity);
    }

    /**
     * 匹配运价， 并计算各个部分的价格
     * 计算之后用dto输出
     * 1、判断是否有对应区域价格
     * 2、判断重量段
     * 3、计算基础运费
     * 4、计算其他费用
     * 5、计算总费用
     *
     * <AUTHOR>
     * 2024-03-05
     */
    public PriceCalculateResultDto getFreight(MatchPriceDto dto, List<VpsChannelCountryFreightEntity> countryFreightEntities) {
        PriceCalculateResultDto resultDto = new PriceCalculateResultDto();
        if (CollectionUtils.isEmpty(countryFreightEntities) || dto.getShipmentWeight() == null) {
            resultDto.setErrorStr("对应的国家价格未找到！");
            return resultDto;
        }
        // 计算区域

        for (VpsChannelCountryFreightEntity countryFreightEntity : countryFreightEntities) {
            // 过滤 国家邮编 或者 区域
            // 如果该运价有开始邮编，则要先匹配上开始邮编
            if (StringUtils.hasText(countryFreightEntity.getStartPostCode()) && !matchCountryPostCode(dto, countryFreightEntity, resultDto)) {
                // 如果没有命中该规则，跳过
                continue;
            }
            LOGGER.info("账单:{}匹配到价格id:{}", dto.getLogisticsNo(), countryFreightEntity.getId());
            // 在此重量范围内
            if (NumberUtil.isIn(dto.getShipmentWeight(), countryFreightEntity.getStartWeight(), countryFreightEntity.getEndWeight())) {
                // 计算偏远等级
                setRemoteAreaLevel(dto, countryFreightEntity, resultDto);
                // 特殊FBA仓库派送费
                checkSpecialPrice(dto, countryFreightEntity);
                // 获取申报信息
                LogisticsDocumentsBaseRequestInfo invoiceInfo = getDeclareInfo(dto, resultDto);
                // 设置目标价格
                resultDto.setTargetPrice(countryFreightEntity.getUnitFreight());
                // 设置价格id
                resultDto.setTargetCountryFreightId(countryFreightEntity.getId());
                // 各部分基础运费（[首重运费+(实际物流重量-首重重量)*续重单价] + 固定运费 + 单价 * 实际物流称重重量）
                BigDecimal basic1 = NumberUtil.mul(countryFreightEntity.getUnitFreight(), dto.getShipmentWeight()).setScale(3, BigDecimal.ROUND_HALF_UP);
                BigDecimal basic2 = NumberUtil.add(countryFreightEntity.getIntiFreight(), NumberUtil.mul(NumberUtil.sub(dto.getShipmentWeight(), countryFreightEntity.getInitWeight()), countryFreightEntity.getExtraFreight())).setScale(3, BigDecimal.ROUND_HALF_UP);
                BigDecimal basic3 = countryFreightEntity.getFixedFreight() == null ? BigDecimal.ZERO : countryFreightEntity.getFixedFreight().setScale(3, BigDecimal.ROUND_HALF_UP);
                // 计算基础运费 (各部分基础运费之和)
                resultDto.setBasicPrice(NumberUtil.mul(NumberUtil.add(basic1, basic2, basic3), countryFreightEntity.getDiscountFactor() == null ? BigDecimal.ONE : countryFreightEntity.getDiscountFactor()).setScale(3, BigDecimal.ROUND_HALF_UP));
                // 挂号费 （目前按照票来计算：固定每单多少钱）
                resultDto.setRegistrationPrice(countryFreightEntity.getRegistrationFreight() == null ? BigDecimal.ZERO : countryFreightEntity.getRegistrationFreight().setScale(3, BigDecimal.ROUND_HALF_UP));
                // 旺季附加费 （实际物流计费重量×旺季附加费单价）
                resultDto.setSurchargePrice(NumberUtil.mul(countryFreightEntity.getOtherFreight(), dto.getShipmentWeight()).setScale(3, BigDecimal.ROUND_HALF_UP));
                // 偏远费
                calculateRemoteAreaFee(dto, resultDto);
                // 燃油费（基础物流费用+偏远费+旺季附加费）* 燃油费率
                resultDto.setFuelPrice(NumberUtil.mul(NumberUtil.add(resultDto.getBasicPrice(), resultDto.getSurchargePrice(), resultDto.getEstimateRemoteAreaFee()), dto.getFuelRate()).setScale(3, BigDecimal.ROUND_HALF_UP));
                // 关税
                calculateTaxFee(dto, invoiceInfo, resultDto);
                // 手续费
                calculateServiceFee(dto, invoiceInfo, resultDto);
                // 总费用(基础物流费用 + 所有其他费用)
                resultDto.setTotalPrice(NumberUtil.add(resultDto.getBasicPrice(), resultDto.getSurchargePrice(), resultDto.getRegistrationPrice(),
                        resultDto.getFuelPrice(), resultDto.getEstimateTax(), resultDto.getEstimateServiceFee(), resultDto.getEstimateRemoteAreaFee()).setScale(2, BigDecimal.ROUND_HALF_UP));
                return resultDto;
            }
        }
        resultDto.setErrorStr("不在配置的重量区间范围内！");
        return resultDto;
    }

    /**
     * true -》命中报价
     * false  未命中
     * 判断 分区名称一致  或者  邮编起始包含
     * <AUTHOR>
     * 2025-06-20
     */
    private boolean matchCountryPostCode(MatchPriceDto dto, VpsChannelCountryFreightEntity countryFreightEntity, PriceCalculateResultDto resultDto) {
        if (StrUtil.isBlank(dto.getPostCode())) {
            // 这一票没有邮编，不符合该单价
            return false;
        }
        // 邮编是自动匹配区域的，先计算区域
        if (StrUtil.equalsIgnoreCase(countryFreightEntity.getStartPostCode(), PackageBillConstant.AREA_MAPPING_POST_CODE) && StrUtil.isNotBlank(countryFreightEntity.getAreaName())) {
            // 查找该账单对应的区域，直接用区域去命中
            String areaName = countryAreaMappingService.findAreaName(countryFreightEntity.getCountryCode(), dto.getPostCode(), countryFreightEntity.getChannelId());
            if (StrUtil.equalsIgnoreCase(areaName, countryFreightEntity.getAreaName())) {
                // 命中邮编区域
                resultDto.setAreaName(areaName);
                return true;
            } else {
                return false;
            }
        }
        // 邮编开头匹配该邮编
        return StrUtil.startWithAnyIgnoreCase(dto.getPostCode(), countryFreightEntity.getStartPostCode().replaceAll("，", ",").replaceAll("\\*", "").split(","));
    }

    // 偏远等级计算
    private void setRemoteAreaLevel(MatchPriceDto dto, VpsChannelCountryFreightEntity countryFreightEntity, PriceCalculateResultDto resultDto) {
        if (StrUtil.equalsIgnoreCase(dto.getLogisticsCompanyEntity().getLogisticsType(), LogisticsTypeEnum.INTERNATIONAL_EXPRESS.name())) {
            // 国际快递
            String areaLevel = areaSurchargeService.findByLogisticsCompanyAndCountryCodeAndPostalCode(dto.getLogisticsCompanyEntity().getLogisticsCompany(), countryFreightEntity.getCountryCode(), dto.getPostCode());
            if (StrUtil.isBlank(areaLevel)) {
                LOGGER.info("{}偏远等级为空！", dto.getLogisticsNo());
            } else {
                resultDto.setLevel(areaLevel);
            }
        } else if (StrUtil.equalsIgnoreCase(dto.getLogisticsCompanyEntity().getLogisticsType(), LogisticsTypeEnum.FREIGHT_FORWARDER.name())) {
            // 货代计算
            String level = areaSurchargeService.findByLogisticsCompanyTypeAndCountryCodeAndPostalCode(dto.getLogisticsCompanyEntity().getLogisticsType(), countryFreightEntity.getCountryCode(), dto.getPostCode());
            resultDto.setLevel(level);
        }
    }

    // 计算手续费用
    private void calculateServiceFee(MatchPriceDto dto, LogisticsDocumentsBaseRequestInfo invoiceInfo, PriceCalculateResultDto resultDto) {
        if (StrUtil.isBlank(invoiceInfo.getStockoutOrderNo()) || CollectionUtils.isEmpty(invoiceInfo.getCommodityList())) {
            LOGGER.info("账单:{}国际快递无制单信息，无法计算手续费用！", dto.getLogisticsNo());
            return;
        }
        if (StrUtil.equalsAnyIgnoreCase(dto.getLogisticsCompanyEntity().getLogisticsMethod(), LogisticsMethodEnum.UPS.getLogisticsMethod())) {
            // UPS手续费计算
            // UPS：等于0.75+(品名数量-1)*3 第一个品名0.75美金，后续每个品名加3美金
            BigDecimal handlingFee = new BigDecimal("0.75");
            if (invoiceInfo.getCommodityList().size() > 1) {
                handlingFee = handlingFee.add(NumberUtil.mul(new BigDecimal(invoiceInfo.getCommodityList().size() - 1), new BigDecimal("3")));
            }
            resultDto.setEstimateServiceFee(handlingFee);
        } else if (StrUtil.equalsAnyIgnoreCase(dto.getLogisticsCompanyEntity().getLogisticsMethod(), LogisticsMethodEnum.FEDEX.getLogisticsMethod(), LogisticsMethodEnum.FEDEX_V2.getLogisticsMethod())) {
            // FEDEX手续费计算
            // FEDEX：3条免费，超过3条每条3.5美金
            BigDecimal handlingFee = BigDecimal.ZERO;
            if (invoiceInfo.getCommodityList().size() > 3) {
                handlingFee = NumberUtil.mul(new BigDecimal(invoiceInfo.getCommodityList().size() - 3), new BigDecimal("3.5"));
            }
            resultDto.setEstimateServiceFee(handlingFee);
        } else if (StrUtil.equalsAnyIgnoreCase(dto.getLogisticsCompanyEntity().getLogisticsMethod(), LogisticsMethodEnum.DHL.getLogisticsMethod())) {
            // DHL手续费计算: 第一个品名免费，第二个10美金，后续每个加20美金
            dhlServiceFee(invoiceInfo, resultDto);
        }
        if (resultDto.getEstimateServiceFee() != null && NumberUtil.isGreater(resultDto.getEstimateServiceFee(), BigDecimal.ZERO)) {
            // 手续费要统一转成rmb
            resultDto.setEstimateServiceFee(NumberUtil.mul(resultDto.getEstimateServiceFee(), getRealTimeExchangeRate(dto)).setScale(2, BigDecimal.ROUND_HALF_UP));
            LOGGER.info("账单:{}国际快递计算手续费成功！费用：{}", dto.getLogisticsNo(), resultDto.getEstimateServiceFee());
        }
    }

    // DHL手续费计算
    private static void dhlServiceFee(LogisticsDocumentsBaseRequestInfo invoiceInfo, PriceCalculateResultDto resultDto) {
        // DHL：第一个品名免费，第二个10美金，后续每个加20美金
        BigDecimal handlingFee = BigDecimal.ZERO;
        if (invoiceInfo.getCommodityList().size() >= 2) {
            handlingFee = BigDecimal.TEN;
            if (invoiceInfo.getCommodityList().size() > 2) {
                handlingFee = handlingFee.add(NumberUtil.mul(new BigDecimal(invoiceInfo.getCommodityList().size() - 2), new BigDecimal("20")));
            }
        }
        resultDto.setEstimateServiceFee(handlingFee);
    }

    private void calculateTaxFee(MatchPriceDto dto, LogisticsDocumentsBaseRequestInfo invoiceInfo, PriceCalculateResultDto resultDto) {
        if (StrUtil.equalsAnyIgnoreCase(dto.getLogisticsCompanyEntity().getLogisticsType(), LogisticsTypeEnum.INTERNATIONAL_EXPRESS.name())) {
            // 目前先按照55%计算关税
            BigDecimal realTimeExchangeRate = getRealTimeExchangeRate(dto);
            resultDto.setEstimateTax(NumberUtil.mul(invoiceInfo.getTotalGoodsPrice(), new BigDecimal("0.55"), realTimeExchangeRate).setScale(2, BigDecimal.ROUND_HALF_UP));
        } else if (StrUtil.equalsAnyIgnoreCase(dto.getLogisticsCompanyEntity().getLogisticsMethod(), LogisticsMethodEnum.FOURPX.getLogisticsMethod())) {
            BigDecimal realTimeExchangeRate = getRealTimeExchangeRate(dto);
            TmsPackageEntity packageEntity = packageNewService.findPackageByLogisticsNo(dto.getLogisticsNo());
            if (packageEntity != null && packageEntity.getInvoicePrice() != null) {
                resultDto.setEstimateTax(NumberUtil.mul(packageEntity.getInvoicePrice(), new BigDecimal("0.45"), realTimeExchangeRate).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
        } else if (dto.getShipmentSkuQty() != null) {
            // 货代 英国计算关税
            BigDecimal realTimeExchangeRate = getRealTimeExchangeRate(dto);
            resultDto.setEstimateTax(realTimeExchangeRate.multiply(new BigDecimal(dto.getShipmentSkuQty())));
        }
        if (resultDto.getEstimateTax() != null && NumberUtil.isGreater(resultDto.getEstimateTax(), BigDecimal.ZERO)) {
            LOGGER.info("账单:{}国际快递计算关税成功！费用：{}", dto.getLogisticsNo(), resultDto.getEstimateTax());
        }
    }


    // 获取汇率，如果汇率不存在，请求fms
    private BigDecimal getRealTimeExchangeRate(MatchPriceDto dto) {
        if (dto.getRealTimeExchangeRate() == null) {
            BigDecimal realTimeExchangeRate = fmsApiService.getRealTimeExchangeRate("USD", dto.getDeliveryDate() == null ? new Date() : dto.getDeliveryDate());
            LOGGER.info("获得汇率：{}", realTimeExchangeRate);
            dto.setRealTimeExchangeRate(realTimeExchangeRate);
            return realTimeExchangeRate;
        }
        return dto.getRealTimeExchangeRate();
    }

    private LogisticsDocumentsBaseRequestInfo getDeclareInfo(MatchPriceDto dto, PriceCalculateResultDto resultDto) {
        LogisticsDocumentsBaseRequestInfo invoiceInfo = new LogisticsDocumentsBaseRequestInfo();
        if (StrUtil.equalsIgnoreCase(dto.getLogisticsCompanyEntity().getLogisticsType(), LogisticsTypeEnum.INTERNATIONAL_EXPRESS.name())
                || StrUtil.isNotBlank(resultDto.getLevel()) && StrUtil.isNotBlank(dto.getLogisticsNo())) {
            LogisticsDocumentsBaseRequestInfo invoiceItemSize = wmsApiService.getInvoiceItemSize(dto.getLogisticsNo(), dto.getLogisticsCompanyEntity().getLocation());
            if (invoiceItemSize == null || invoiceItemSize.getCommodityList() == null) {
                LOGGER.info("未获取到正确的申报条目：{}", dto.getLogisticsNo());
            } else {
                invoiceInfo = invoiceItemSize;
            }
        }
        return invoiceInfo;
    }

    private void checkSpecialPrice(MatchPriceDto dto, VpsChannelCountryFreightEntity countryFreightEntity) {
        if (StrUtil.isBlank(countryFreightEntity.getRemark()) || StrUtil.isBlank(dto.getDestinationFulfillmentCenterId())) {
            return;
        }
        String regex = "\\((.*?)\\)";
        List<String> remarkList = ReUtil.findAll(regex, countryFreightEntity.getRemark(), 1);
        for (String remarkDetail : remarkList) {
            if (!StrUtil.contains(remarkDetail, "额外仓库收费")) {
                continue;
            }
            String regexTarget = "\\[([^\\]]+)\\]";
            List<String> remarkTargetList = ReUtil.findAll(regexTarget, remarkDetail, 1);
            for (String remarkTarget : remarkTargetList) {
                if (StrUtil.contains(remarkTarget, dto.getDestinationFulfillmentCenterId())) {
                    String regexPrice = "【([^【】]+)】";
                    List<String> remarkPriceList = ReUtil.findAll(regexPrice, remarkDetail, 1);
                    if (!CollectionUtils.isEmpty(remarkPriceList) && NumberUtil.isNumber(remarkPriceList.get(0))) {
                        LOGGER.info("账单:{}有额外的附加费用", dto.getLogisticsNo());
                        countryFreightEntity.setUnitFreight(countryFreightEntity.getUnitFreight().add(new BigDecimal(remarkPriceList.get(0))));
                    }
                }
            }
        }
    }


    public void dealDifference(PackageBillUpdateRequest updateRequest) {
        PackageBillEntity bill = getById(updateRequest.getId());
        if (bill == null) {
            throw new BusinessServiceException(updateRequest.getId() + "无法找到该账单！");
        }
        bill.setRealBasicFreight(updateRequest.getRealBasicFreight());
        bill.setRealOtherFreight(updateRequest.getRealOtherFreight());
        bill.setAttachmentName(updateRequest.getAttachmentName());
        bill.setAttachmentUrl(updateRequest.getAttachmentUrl());
        bill.setShipmentWeight(updateRequest.getShipmentWeight());
        bill.setShipmentVolumeWeight(updateRequest.getShipmentVolumeWeight());
        bill.setRealWeight(updateRequest.getRealWeight());
        bill.setUserRemark(updateRequest.getUserRemark());
        bill.setUpdateBy(accessControlService.getUserName());
        updateByIdIgnoreTenant(bill);
    }

    public void updateReviewResult(PackageBillUpdateBatchRequest updateRequest) {
        List<PackageBillEntity> packageBillEntities = listByIds(updateRequest.getIdList());
        packageBillEntities.forEach(bill -> {
            bill.setReviewResult(updateRequest.getReviewResult());
            bill.setUpdateBy(accessControlService.getUserName());
            updateByIdIgnoreTenant(bill);
        });
    }

    // 预估 且  差异核对
    @Transactional(rollbackFor = Exception.class)
    public void verify(List<Integer> idList, String operate) {
        List<PackageBillEntity> billEntities = listByIds(idList);
        billEntities.forEach(bill -> {
            reGetWeight(bill);
            // 预估价格
            evaluateBill(bill.getLogisticsNo());
            // 差异核对
            if (bill.getRealWeight() != null || "账单导入".equals(operate)) {
                checkDiff(bill.getLogisticsNo());
            }
        });
    }

    private void reGetWeight(PackageBillEntity bill) {
        bill.setShipmentWeight(BigDecimal.ZERO);
        bill.setShipmentVolumeWeight(BigDecimal.ZERO);
        List<StockoutShipmentEntity> shipmentList = shipmentService.findByLogisticsNo(bill.getLogisticsNo());
        if (CollectionUtils.isEmpty(shipmentList)) {
            return;
        }
        shipmentList.forEach(shipment -> {
            bill.setShipmentWeight(bill.getShipmentWeight().add(shipment.getWeight() == null ? BigDecimal.ZERO : shipment.getWeight()));
            bill.setShipmentVolumeWeight(bill.getShipmentVolumeWeight().add(shipment.getVolumeWeight() == null ? BigDecimal.ZERO : shipment.getVolumeWeight()));
        });
        TmsLogisticsCompanyEntity logisticsCompanyEntity = logisticsCompanyRepository.findById(bill.getLogisticsCompanyId())
                .orElseThrow(() -> new BusinessServiceException(String.format("物流公司【%s】不存在，账单自动预算价格失败！", bill.getLogisticsCompanyId())));
        bill.setChargeWeight(ChargeWeightTypeEnum.calculateByName(logisticsCompanyEntity.getChargeWeightType(), bill.getShipmentWeight()));
        if (StrUtil.equalsIgnoreCase(logisticsCompanyEntity.getLogisticsType(), LogisticsTypeEnum.INTERNATIONAL_EXPRESS.name())) {
            BigDecimal volumeWeight = NumberUtil.mul(bill.getShipmentVolumeWeight(), new BigDecimal("1.15"));
            if (NumberUtil.isLess(bill.getShipmentWeight(), volumeWeight)) {
                bill.setChargeWeight(ChargeWeightTypeEnum.calculateByName(logisticsCompanyEntity.getChargeWeightType(), volumeWeight));
            }
            bill.setRealChargeWeight(ChargeWeightTypeEnum.calculateByName(logisticsCompanyEntity.getChargeWeightType(), bill.getRealWeight()));
        }
        updateByIdIgnoreTenant(bill);
    }

    private void checkDiff(String logisticsNo) {
        PackageBillEntity billEntity = packageBillPermissionService.getPackageBillWithPermission(logisticsNo);
        if (billEntity == null) {
            LOGGER.error("{}找不到账单，差异核对失败！", logisticsNo);
            return;
        }
        List<String> errorList = new ArrayList<>();
        validBill(billEntity, errorList);
        if (!CollectionUtils.isEmpty(errorList)) {
            String errorMsg = String.join(";", errorList);
            LOGGER.error("{}差异核对失败!", errorMsg);
            billEntity.setSystemRemark(StringUtils.hasText(billEntity.getSystemRemark()) ? billEntity.getSystemRemark() + ";" + errorMsg : errorMsg);
            updateByIdIgnoreTenant(billEntity);
            return;
        }
        // 开始差异核对
        billEntity.setDiffWeight(NumberUtil.sub(billEntity.getRealChargeWeight() == null ? billEntity.getRealWeight() : billEntity.getRealChargeWeight(), billEntity.getChargeWeight()));
        billEntity.setDiffFreight(NumberUtil.sub(NumberUtil.add(billEntity.getRealBasicFreight(), billEntity.getRealOtherFreight()), billEntity.getEstimateTotalPrice()));
        // 设置差异类型，设置核对状态
        if (BigDecimal.ZERO.compareTo(billEntity.getDiffWeight()) != 0 && BigDecimal.ZERO.compareTo(billEntity.getDiffFreight()) != 0) {
            billEntity.setDifferenceType(LogisticsDifferenceTypeEnum.ALL_DIFFERENCE.getCode());
            billEntity.setStatus(PackageBillStatusEnum.DONE.name());
            billEntity.setReviewResult(0);
        } else if (BigDecimal.ZERO.compareTo(billEntity.getDiffWeight()) != 0) {
            billEntity.setDifferenceType(LogisticsDifferenceTypeEnum.WEIGHT_DIFFERENCE.getCode());
            billEntity.setReviewResult(0);
            billEntity.setStatus(PackageBillStatusEnum.DONE.name());
        } else if (BigDecimal.ZERO.compareTo(billEntity.getDiffFreight()) != 0) {
            billEntity.setDifferenceType(LogisticsDifferenceTypeEnum.PRICE_DIFFERENCE.getCode());
            billEntity.setReviewResult(0);
            billEntity.setStatus(PackageBillStatusEnum.DONE.name());
        } else {
            billEntity.setStatus(PackageBillStatusEnum.DONE.name());
            billEntity.setReviewResult(1);
            billEntity.setDifferenceType(LogisticsDifferenceTypeEnum.NO_DIFFERENCE.getCode());
            billEntity.setPaymentFreight(NumberUtil.add(billEntity.getRealBasicFreight(), billEntity.getRealOtherFreight()));
        }
        billEntity.setUpdateBy(accessControlService.getUserName());
        buildDiffCause(billEntity);
        if (billEntity.getDifferenceCause() == null) {
            this.update(new UpdateWrapper<PackageBillEntity>().lambda().set(PackageBillEntity::getDifferenceCause, null)
                    .eq(PackageBillEntity::getId, billEntity.getId()));
        }
        updateByIdIgnoreTenant(billEntity);
    }

    private void buildDiffCause(PackageBillEntity billEntity) {
        BigDecimal fifteenDiff = new BigDecimal("0.015");
        BigDecimal threeThousand = new BigDecimal("3");
        VpsChannelCountryFreightEntity countryFreightEntity = countryFreightService.getById(billEntity.getCountryFreightId());
        if (countryFreightEntity == null) {
            billEntity.setSystemRemark("往期账单或者功能未上线！");
            return;
        }
        boolean sameFreight = NumberUtil.equals(billEntity.getDiffFreight(), BigDecimal.ZERO);
        if (StrUtil.isBlank(billEntity.getAccount())) {
            smallPackage(billEntity, fifteenDiff, countryFreightEntity, sameFreight);
            return;
        }
        // 国际快递账单
        expressLogistics(billEntity, threeThousand, countryFreightEntity, sameFreight);
    }

    private void expressLogistics(PackageBillEntity billEntity, BigDecimal threeThousand, VpsChannelCountryFreightEntity countryFreightEntity, boolean sameFreight) {
        // 1、差异重量 在0-3之间
        if (NumberUtil.isGreater(billEntity.getDiffWeight(), BigDecimal.ZERO) && NumberUtil.isLessOrEqual(billEntity.getDiffWeight(), threeThousand)) {
            if (sameFreight) {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.RATIONAL.getCode());
                billEntity.setStatus(PackageBillStatusEnum.DONE.name());
                return;
            }
            causeByBillType(billEntity, countryFreightEntity);
            return;
        }
        // 重量差异大于 3kg
        if (NumberUtil.isGreater(billEntity.getDiffWeight(), threeThousand)) {
            BigDecimal sub = NumberUtil.sub(billEntity.getRealChargeWeight(), billEntity.getProductWeight());
            if (NumberUtil.isLessOrEqual(sub, threeThousand)) {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.SPACE.getCode());
            } else if (NumberUtil.isGreater(sub, threeThousand)) {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.LOGISTICS.getCode());
            }
            return;
        }
        // 重量差异 = 0
        if (NumberUtil.equals(billEntity.getDiffWeight(), BigDecimal.ZERO)) {
            if (sameFreight) {
                billEntity.setStatus(PackageBillStatusEnum.DONE.name());
                billEntity.setReviewResult(1);
                return;
            }
            // 是 固定费用模式 还是 单价*重量
            billEntity.setDifferenceType(LogisticsDifferenceTypeEnum.PRICE_DIFFERENCE.getCode());
            causeByBillType(billEntity, countryFreightEntity);
            return;
        }
        // 重量差异 < 0
        if (NumberUtil.isLess(billEntity.getDiffWeight(), BigDecimal.ZERO)) {
            if (NumberUtil.isLessOrEqual(billEntity.getDiffFreight(), BigDecimal.ZERO)) {
                billEntity.setStatus(PackageBillStatusEnum.DONE.name());
                billEntity.setReviewResult(1);
                billEntity.setDifferenceType(LogisticsDifferenceTypeEnum.NO_DIFFERENCE.getCode());
                billEntity.setDifferenceCause(null);
                return;
            }
            billEntity.setDifferenceType(LogisticsDifferenceTypeEnum.PRICE_DIFFERENCE.getCode());
            causeByBillType(billEntity, countryFreightEntity);
            return;
        }
    }

    private void causeByBillType(PackageBillEntity billEntity, VpsChannelCountryFreightEntity countryFreightEntity) {
        // 是 固定费用 模式
        if (StrUtil.equalsAnyIgnoreCase(countryFreightEntity.getWeightBillType(), BillingTypeChannelEnum.RANGE_WEIGHT.name())) {
            if (NumberUtil.equals(countryFreightEntity.getFixedFreight(), billEntity.getRealBasicFreight())) {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.OTHER.getCode());
            } else {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.BASIC.getCode());
            }
            return;
        }
        if (StrUtil.equalsAnyIgnoreCase(countryFreightEntity.getWeightBillType(), BillingTypeChannelEnum.UNIT_PRICE_WEIGHT.name())) {
            BigDecimal div = NumberUtil.div(billEntity.getRealBasicFreight(), billEntity.getRealChargeWeight(), 3);
            if (NumberUtil.equals(countryFreightEntity.getUnitFreight(), div)) {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.OTHER.getCode());
            } else {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.BASIC.getCode());
            }
            return;
        }
    }

    private void smallPackage(PackageBillEntity billEntity, BigDecimal fifteenDiff, VpsChannelCountryFreightEntity countryFreightEntity, boolean sameFreight) {
        // 小包差异状态----差异重量
        // 1、差异重量 在0-15之间
        if (NumberUtil.isGreater(billEntity.getDiffWeight(), BigDecimal.ZERO) && NumberUtil.isLessOrEqual(billEntity.getDiffWeight(), fifteenDiff)) {
            if (sameFreight) {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.RATIONAL.getCode());
                billEntity.setStatus(PackageBillStatusEnum.DONE.name());
                billEntity.setReviewResult(1);
                return;
            }
            // 【实际基础物流费用】/【实际物流重量】
            BigDecimal div = NumberUtil.div(billEntity.getRealBasicFreight(), billEntity.getRealWeight(), 3);
            if (NumberUtil.equals(div, countryFreightEntity.getUnitFreight())) {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.OTHER_FREIGHT.getCode());
            } else {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.BASIC_FREIGHT.getCode());
            }
        } else if (NumberUtil.isGreater(billEntity.getDiffWeight(), fifteenDiff)) {
            // 2、差异重量 大于15
            // 实际物流重量-商品实际总重量
            BigDecimal sub = NumberUtil.sub(billEntity.getRealWeight(), billEntity.getProductWeight());
            // 在小于等于15
            if (NumberUtil.isLessOrEqual(sub, fifteenDiff)) {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.SPACE.getCode());
            } else {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.LOGISTICS.getCode());
            }
        } else if (NumberUtil.equals(billEntity.getDiffWeight(), BigDecimal.ZERO)) {
            // 3、差异重量 等于0
            if (sameFreight) {
                billEntity.setDifferenceCause(null);
                billEntity.setStatus(PackageBillStatusEnum.DONE.name());
                billEntity.setReviewResult(1);
                return;
            }
            BigDecimal div = NumberUtil.div(billEntity.getRealBasicFreight(), billEntity.getRealWeight(), 3);
            if (NumberUtil.equals(div, countryFreightEntity.getUnitFreight())) {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.OTHER.getCode());
            } else {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.BASIC.getCode());
            }
        } else if (NumberUtil.isLess(billEntity.getDiffWeight(), BigDecimal.ZERO)) {
            // 3、差异重量 小于0
            if (NumberUtil.isLessOrEqual(billEntity.getDiffFreight(), BigDecimal.ZERO)) {
                billEntity.setDifferenceCause(null);
                billEntity.setStatus(PackageBillStatusEnum.DONE.name());
                billEntity.setDifferenceType(LogisticsDifferenceTypeEnum.NO_DIFFERENCE.getCode());
                billEntity.setReviewResult(1);
                return;
            }
            billEntity.setDifferenceType(LogisticsDifferenceTypeEnum.PRICE_DIFFERENCE.getCode());
            BigDecimal div = NumberUtil.div(billEntity.getRealBasicFreight(), billEntity.getRealWeight(), 3);
            if (NumberUtil.equals(div, countryFreightEntity.getUnitFreight())) {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.OTHER.getCode());
            } else {
                billEntity.setDifferenceCause(LogisticsDifferenceCauseEnum.BASIC.getCode());
            }
        }
    }

    private void validBill(PackageBillEntity billEntity, List<String> errorList) {
        if (billEntity.getEstimateTotalPrice() == null) {
            errorList.add("预估物流总费用未计算");
        }
        if (billEntity.getChargeWeight() == null) {
            errorList.add("仓库计费重量未填写");
        }
        if (billEntity.getRealWeight() == null) {
            errorList.add("实际重量未填写");
        }
        if (billEntity.getRealBasicFreight() == null) {
            errorList.add("实际基础运费未填写");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void importBill(PackageBillImport row, UploadRequest request, Map<Integer, TmsLogisticsCompanyEntity> companyEntityMap, Integer mode) {
        PackageBillEntity billEntity = packageBillPermissionService.getPackageBillWithPermission(row.getLogisticsNo());
        if (billEntity == null) {
            billEntity = generateBillInImport(row);
        }
        if (StrUtil.isNotBlank(billEntity.getBillName()) && !StrUtil.equals(billEntity.getBillName(), request.getFileName()) && mode == 1) {
            throw new BusinessServiceException(row.getLogisticsNo() + "覆盖历史账单失败！历史账单文件名：" + billEntity.getBillName());
        }
        if (StrUtil.isNotBlank(billEntity.getBillName()) && StrUtil.equals(billEntity.getBillName(), request.getFileName()) && mode == 2) {
            throw new BusinessServiceException(row.getLogisticsNo() + "差额计算失败！是否重复导入差额？历史账单文件名：" + billEntity.getBillName());
        }
        if (mode == 1) {
            billEntity.setRealWeight(row.getRealWeight());
        } else {
            billEntity.setRealWeight(NumberUtil.add(billEntity.getRealWeight(), row.getRealWeight()));
        }
        Integer logisticsCompanyId = billEntity.getLogisticsCompanyId();
        TmsLogisticsCompanyEntity logisticsCompanyEntity = companyEntityMap.computeIfAbsent(logisticsCompanyId, one -> logisticsCompanyRepository.findById(logisticsCompanyId)
                .orElseThrow(() -> new BusinessServiceException(String.format("物流公司【%s】不存在，账单导入失败！", logisticsCompanyId))));
        if (StrUtil.equalsIgnoreCase(logisticsCompanyEntity.getLogisticsType(), LogisticsTypeEnum.INTERNATIONAL_EXPRESS.name())) {
            billEntity.setRealChargeWeight(ChargeWeightTypeEnum.calculateByName(logisticsCompanyEntity.getChargeWeightType(), billEntity.getRealWeight()));
        }
        setRealPrice(row, mode, billEntity);
        billEntity.setCheckDate(new Date());
        billEntity.setBillName(request.getFileName());
        billEntity.setBusinessDate(row.getBusinessDate());
        billEntity.setBillUrl(request.getUploadFileUrl());
        billEntity.setTempBasicFreightDiff(NumberUtil.sub(billEntity.getRealBasicFreight(), billEntity.getTempBasicFreight()));
        billEntity.setTempOtherFreightDiff(NumberUtil.sub(billEntity.getRealOtherFreight(), billEntity.getTempOtherFreight()));
        updateByIdIgnoreTenant(billEntity);
        try {
            verify(Collections.singletonList(billEntity.getId()), "账单导入");
        } catch (Exception e) {
            LOGGER.info("导入账单初始化和核对失败：{}", e.getMessage());
        }
    }

    private static void setRealPrice(PackageBillImport row, Integer mode, PackageBillEntity billEntity) {
        // 根据mode字段进行增量或全量赋值
        if (mode == 1) {
            // 全量模式：覆盖所有字段
            billEntity.setRealBasicFreight(row.getRealBasicFreight());
            billEntity.setRealOtherFreight(row.getRealOtherFreight() == null ? BigDecimal.ZERO : row.getRealOtherFreight());
            billEntity.setRealFuelPrice(row.getRealFuelPrice() == null ? BigDecimal.ZERO : row.getRealFuelPrice());
            billEntity.setRealSurchargePrice(row.getRealSurchargePrice() == null ? BigDecimal.ZERO : row.getRealSurchargePrice());
            billEntity.setRealRemoteAreaFee(row.getRealRemoteAreaFee() == null ? BigDecimal.ZERO : row.getRealRemoteAreaFee());
            billEntity.setRealTax(row.getRealTax() == null ? BigDecimal.ZERO : row.getRealTax());
            billEntity.setRealServiceFee(row.getRealServiceFee() == null ? BigDecimal.ZERO : row.getRealServiceFee());
            billEntity.setRealRegistrationPrice(row.getRealRegistrationPrice() == null ? BigDecimal.ZERO : row.getRealRegistrationPrice());
        } else if (mode == 2) {
            // 增量模式：使用NumberUtil.add进行累加操作
            billEntity.setRealBasicFreight(NumberUtil.add(billEntity.getRealBasicFreight(), row.getRealBasicFreight()));
            billEntity.setRealOtherFreight(NumberUtil.add(billEntity.getRealOtherFreight(), row.getRealOtherFreight()));
            billEntity.setRealFuelPrice(NumberUtil.add(billEntity.getRealFuelPrice(), row.getRealFuelPrice()));
            billEntity.setRealSurchargePrice(NumberUtil.add(billEntity.getRealSurchargePrice(), row.getRealSurchargePrice()));
            billEntity.setRealRemoteAreaFee(NumberUtil.add(billEntity.getRealRemoteAreaFee(), row.getRealRemoteAreaFee()));
            billEntity.setRealTax(NumberUtil.add(billEntity.getRealTax(), row.getRealTax()));
            billEntity.setRealServiceFee(NumberUtil.add(billEntity.getRealServiceFee(), row.getRealServiceFee()));
            billEntity.setRealRegistrationPrice(NumberUtil.add(billEntity.getRealRegistrationPrice(), row.getRealRegistrationPrice()));
        }
        // 计算实际物流总费用：实际基础物流费用+实际燃油费+实际偏远费+实际关税+实际挂号费+实际手续费+实际附加费+实际其他费用
        BigDecimal realTotalPrice = NumberUtil.add(
            billEntity.getRealBasicFreight(),
            billEntity.getRealFuelPrice(),
            billEntity.getRealRemoteAreaFee(),
            billEntity.getRealTax(),
            billEntity.getRealRegistrationPrice(),
            billEntity.getRealServiceFee(),
            billEntity.getRealSurchargePrice(),
            billEntity.getRealOtherFreight()
        );
        billEntity.setRealTotalPrice(realTotalPrice);
    }

    private PackageBillEntity generateBillInImport(PackageBillImport row) {
        PackageBillEntity billEntity;
        //也根据配置的信息去找信息去找,找不到则表示没有对应地区的权限
        TmsPackageEntity packageEntity = packageNewService.findPackageByLogisticsNoWithPermission(row.getLogisticsNo());
        if (packageEntity == null) {
            throw new BusinessServiceException(row.getLogisticsNo() + "无法找到对应的物流包裹！");
        }
        // 尝试创建账单
        shipmentService.buildBill(row.getLogisticsNo());
        billEntity = packageBillPermissionService.getPackageBillWithPermission(row.getLogisticsNo());
        if (billEntity == null) {
            throw new BusinessServiceException(row.getLogisticsNo() + "找不到账单，差异核对失败！");
        } else {
            LOGGER.info("导入才初始化账单：{}", row.getLogisticsNo());
        }
        return billEntity;
    }

    /**
     * 忽略租户更新账单
     *
     * @param billEntity 账单实体
     * @return 是否更新成功
     */
    public boolean updateByIdIgnoreTenant(PackageBillEntity billEntity) {
        IgnoreLocationContext.setIgnoreLocation(true);
        try {
            return updateById(billEntity);
        } finally {
            IgnoreLocationContext.setIgnoreLocation(false);
        }
    }

    public String financialPush(List<Integer> idList) {
        List<String> errorList = new ArrayList<>();
        List<PackageBillEntity> billEntities = listByIds(idList);
        billEntities.forEach(bill -> {
            SpringUtil.getBean(PackageBillService.class).validAndPush(errorList, bill);
        });
        return String.join(";", errorList);
    }

    /**
     * 验证并推送
     *
     * @param errorList 错误列表
     * @param bill 账单实体
     */
    @Transactional(rollbackFor = Exception.class)
    public void validAndPush(List<String> errorList, PackageBillEntity bill) {
        // 已二次推送,不允许再次推送
        if (bill.getFinancialPushStatus() != null && bill.getFinancialPushStatus() == 2) {
            errorList.add(bill.getLogisticsNo() + "物流单号已二次推送差异，不允许再次推送");
            return;
        }
        // 首次推送
        if (bill.getFinancialPushStatus() == null || bill.getFinancialPushStatus() == 0) {
            if (bill.getRealBasicFreight() == null || bill.getRealOtherFreight() == null) {
                errorList.add(bill.getLogisticsNo() + "物流单号缺少实际基础物流费用或实际其他费用，不允许推送");
                return;
            }
            bill.setFinancialPushStatus(1);
            bill.setTempBasicFreight(bill.getRealBasicFreight());
            bill.setTempOtherFreight(bill.getRealOtherFreight() == null ? BigDecimal.ZERO : bill.getRealOtherFreight());
        } else if (bill.getFinancialPushStatus() == 1) {
            // 计算暂估与实际费用差异
            bill.setTempBasicFreightDiff(NumberUtil.sub(bill.getRealBasicFreight(), bill.getTempBasicFreight()));
            bill.setTempOtherFreightDiff(NumberUtil.sub(bill.getRealOtherFreight(), bill.getTempOtherFreight()));
            // 二次推送时,需要校验暂估与实际费用差异
            if (bill.getTempBasicFreightDiff() == null || bill.getTempOtherFreightDiff() == null) {
                errorList.add(bill.getLogisticsNo() + "物流单号缺少暂估差异，不允许二次推送");
                return;
            }
            if (bill.getTempBasicFreightDiff().compareTo(BigDecimal.ZERO) == 0 && bill.getTempOtherFreightDiff().compareTo(BigDecimal.ZERO) ==  0) {
                errorList.add(bill.getLogisticsNo() + "物流单号无暂估差异，不允许二次推送");
                return;
            }
            bill.setFinancialPushStatus(2);
        } else {
            errorList.add(bill.getLogisticsNo() + "物流单号推送状态异常，不允许再次推送");
            return;
        }
        // 开始推送
        // 推送完更新账单
        updateByIdIgnoreTenant(bill);
    }

}
