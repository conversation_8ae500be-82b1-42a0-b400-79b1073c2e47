package com.nsy.api.tms.service.external.refactor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.annotation.LogisticsServiceHandler;
import com.nsy.api.tms.dao.entity.TmsLogisticsAccountEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.domain.Address;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.domain.order.OrderNewInfo;
import com.nsy.api.tms.enumeration.LocationEnum;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.enumeration.StockTransferTrackingStatusEnum;
import com.nsy.api.tms.filter.TenantContext;
import com.nsy.api.tms.logistics.cangsou.v2.TmsGetStockInOrderInfo;
import com.nsy.api.tms.logistics.goodcang.GoodCangGetStockResponse;
import com.nsy.api.tms.logistics.goodcang.GoodCangItem;
import com.nsy.api.tms.logistics.goodcang.GoodCangLabelInfo;
import com.nsy.api.tms.logistics.goodcang.GoodCangOrderRequest;
import com.nsy.api.tms.logistics.goodcang.GoodCangResponse;
import com.nsy.api.tms.logistics.goodcang.GoodCangTransferRequest;
import com.nsy.api.tms.logistics.goodcang.GoodCangUploadPdfRequest;
import com.nsy.api.tms.logistics.goodcang.SenderInfo;
import com.nsy.api.tms.logistics.goodcang.stockin.BoxDetails;
import com.nsy.api.tms.logistics.goodcang.stockin.CreateStockInOrderRequest;
import com.nsy.api.tms.logistics.goodcang.stockin.GoodCangBaseResponse;
import com.nsy.api.tms.logistics.goodcang.stockin.GoodCangCreateStockInOrderResponse;
import com.nsy.api.tms.logistics.goodcang.stockin.GoodCangShipmentLabelRequest;
import com.nsy.api.tms.logistics.goodcang.stockin.GoodCangSkuBarcodeRequest;
import com.nsy.api.tms.logistics.goodcang.stockin.GoodCangSkuBarcodeResponse;
import com.nsy.api.tms.logistics.goodcang.stockin.GoodCangStockInOrderInfo;
import com.nsy.api.tms.logistics.goodcang.stockin.GoodCangStockInOrderInfoRequest;
import com.nsy.api.tms.logistics.goodcang.stockin.GoodCangStockInOrderInfoResponse;
import com.nsy.api.tms.logistics.goodcang.stockin.GoodCangStockInOrderNoResponse;
import com.nsy.api.tms.logistics.goodcang.stockin.StockInOrderBox;
import com.nsy.api.tms.logistics.goodcang.stockin.StockTransferTrackingResponse;
import com.nsy.api.tms.logistics.goodcang.stockin.WmsCreateStockInOrderRequest;
import com.nsy.api.tms.request.OrderNewRequest;
import com.nsy.api.tms.request.OverseaStockRequest;
import com.nsy.api.tms.request.SkuDTO;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.response.SecondaryNumberResponse;
import com.nsy.api.tms.response.base.BaseStringResponse;
import com.nsy.api.tms.service.SpaceSkuMappingService;
import com.nsy.api.tms.service.external.refactor.winit.model.OverseaPrintShipmentLabelRequest;
import com.nsy.api.tms.utils.JsonMapper;
import com.nsy.api.tms.utils.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * HXD
 * 2021/5/11
 **/
@Service
@LogisticsServiceHandler(logisticsMethod = LogisticsMethodEnum.GOODCANG)
public class GoodCangNewService extends BaseLogisticsNewService implements InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(GoodCangNewService.class);

    public static final String SHIPPING_METHOD = "shipping_method";
    public static final String DISTRIBUTOR_TYPE = "distributor_type";
    public static final String WAREHOUSE_CODE = "warehouse_code";
    public static final String WAREHOUSE_CODE_STOCK_IN = "warehouse_code_stock_in";
    public static final String VERIFY_STOCK_IN = "verify_stock_in";
    public static final String RECEIVING_SHIPPING_TYPE = "receiving_shipping_type";
    public static final String PRINT_SIZE_CODE = "print_size_code";
    public static final String APP_TOKEN = "app-token";
    public static final String APP_KEY = "app-key";
    // 自动审核单，前期默认0
    public static final String AUTO_CREATE_ORDER = "auto_create_order";

    @Value("${goodcang.server.url}")
    private String goodcangServiceURL;
    @Value("${goodcang.server.upload.pdf.url}")
    private String goodcangUploadPdfURL;
    @Value("${goodcang.server.transfer}")
    private String goodcangTransferURL;
    @Value("${goodcang.server.create.grn}")
    private String goodcangCreateGrn;
    @Value("${goodcang.server.get.grn.detail}")
    private String goodcangGetGrnDetail;
    @Value("${goodcang.server.get.sku.barcode}")
    private String goodcangGetSkuBarcode;
    @Value("${goodcang.server.print.inbound.order.shipment}")
    private String printGoodCangShipment;
    @Value("${goodcang.server.get.inventory}")
    private String getStock;
    @Value("${goodcang.server.cancel.order}")
    private String cancelOrder;
    @Autowired
    private SpaceSkuMappingService spaceSkuMappingService;

    public static final String LABEL_NAME = "%sGoodCang-%s";

    @Inject
    private RestTemplate restTemplate;

    @Override
    protected GoodCangOrderRequest buildLogisticsOrderRequest(OrderNewRequest orderRequest, Map<String, String> configMap) {
        GoodCangOrderRequest request = new GoodCangOrderRequest();
        OrderNewInfo orderInfo = orderRequest.getOrderInfo();
        request.setReferenceNo(orderInfo.getBusinessKey());
        if (StrUtil.containsIgnoreCase(orderInfo.getPlatform(), "TikTok")) {
            request.setPlatform("TIKTOK");
        } else if (StrUtil.containsIgnoreCase(orderInfo.getPlatform(), "Temu")) {
            request.setPlatform("TEMU");
        } else if (StrUtil.containsIgnoreCase(orderInfo.getPlatform(), "SHEIN")) {
            request.setPlatform("SHEIN");
        } else {
            request.setPlatform("OTHER");
        }
        request.setShippingMethod(orderInfo.getLogisticsChannelCode());
        if (StrUtil.startWithIgnoreCase(orderInfo.getLogisticsChannelCode(), "wh") && StrUtil.hasBlank(orderInfo.getMappingLogisticsNoLabel(), orderInfo.getMappingLogisticsNo())) {
            throw new InvalidRequestException("自提必须有物流单号和面单");
        }
        if (StrUtil.startWithIgnoreCase(orderInfo.getLogisticsChannelCode(), "wh") && StrUtil.equals(orderInfo.getLocation(), LocationEnum.QUANZHOU.name())) {
            request.setDistributorType(1);
            request.setPlatform("TEMU");
        }
        request.setPlatformOrderCode(orderInfo.getTid());
        request.setWarehouseCode(configMap.get(WAREHOUSE_CODE));
        request.setOrderDesc(orderInfo.getStoreName());
        request.setCountryCode(orderInfo.getReceiveCountryCode());
        request.setCity(orderInfo.getReceiver().getCity());
        request.setCompany(orderInfo.getReceiver().getCompany());
        String street = orderInfo.getReceiver().getStreet();
        if (street.length() > 100) {
            throw new BusinessServiceException("收件人地址不能大于100个字符");
        }
        if (street.length() <= 32) {
            request.setAddress1(street);
        } else {
            // 从第32个字符向前找到最近的空格
            int splitIndex = 32;
            while (splitIndex > 0 && !Character.isWhitespace(street.charAt(splitIndex - 1))) {
                splitIndex--;
            }
            if (splitIndex == 0) {
                throw new BusinessServiceException("收件人地址错误，超过50个字符，且没有空格");
            }
            request.setAddress1(street.substring(0, splitIndex).trim());
            request.setAddress2(street.substring(splitIndex).trim());
        }
        request.setZipcode(orderInfo.getReceiver().getPostCode());
        request.setProvince(orderInfo.getReceiver().getProvince());
        request.setName(orderInfo.getReceiver().getName());
        request.setPhone(StringUtils.hasText(orderInfo.getReceiver().getPhone()) ? orderInfo.getReceiver().getPhone() : orderInfo.getReceiver().getMobile());
        request.setEmail(orderInfo.getReceiver().getEmail());
        request.setVerify(Integer.valueOf(configMap.get(AUTO_CREATE_ORDER)));
        request.setIsSignature(0);
        request.setItems(buildItems(orderInfo));
        request.setSenderInfo(buildSender(orderInfo.getSender()));
        return request;
    }

    private List<GoodCangItem> buildItems(OrderNewInfo orderInfo) {
        List<GoodCangItem> collect = new ArrayList<>();
        Map<String, Integer> skuQtyMap = orderInfo.getOrderItemInfoList().stream().collect(Collectors.groupingBy(OrderItemInfo::getSku,
                Collectors.summingInt(OrderItemInfo::getCount)));

        skuQtyMap.forEach((sku, qty) -> {
            String skuMapping = spaceSkuMappingService.findSpaceSkuBySku(sku, orderInfo.getLocation(), orderInfo.getSpaceName());
            GoodCangItem goodCangItem = new GoodCangItem();
            goodCangItem.setProductSku(skuMapping);
            goodCangItem.setQuantity(qty);
            collect.add(goodCangItem);
        });
        return collect;
    }


    private SenderInfo buildSender(Address sysSender) {
        SenderInfo sender = new SenderInfo();
        sender.setName(sysSender.getName());
        sender.setPhone(StringUtils.hasText(sysSender.getPhone()) ? sysSender.getPhone() : sysSender.getMobile());
        return sender;
    }

    @Override
    public SecondaryNumberResponse getSecondaryNumber(TmsPackageEntity packageEntity) {
        if (StringUtils.hasText(packageEntity.getSecondaryNumber()) && !PackageStatusEnum.CREATED.getDesc().equalsIgnoreCase(packageEntity.getStatus())) {
            return super.getSecondaryNumber(packageEntity);
        }
        Map<String, String> configMap;
        if (StrUtil.isNotBlank(packageEntity.getKeyGroup())) {
            configMap = configService.getConfigMap(packageEntity.getKeyGroup());
        } else {
            LOGGER.error("使用旧账号配置获取海外仓订单！");
            configMap = logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());
        }
        GoodCangTransferRequest request = new GoodCangTransferRequest();
        request.setOrderCode(packageEntity.getLogisticsTid());
        GoodCangResponse goodCangResponse = postToGoodCang(JsonMapper.toJson(request), configMap, goodcangTransferURL, GoodCangResponse.class);
        if (goodCangResponse == null || goodCangResponse.getData() == null) {
            // 该订单被海外仓人员被删除了
            packageEntity.setStatus(PackageStatusEnum.CANCEL.getDesc());
            packageService.save(packageEntity);
            LOGGER.info("{}取消包裹", JsonMapper.toJson(packageEntity));
            return super.getSecondaryNumber(packageEntity);
        }
        if (StringUtils.hasText(goodCangResponse.getData().getTrackingNo()) && StrUtil.isBlank(packageEntity.getSecondaryNumber())) {
            packageEntity.setSecondaryNumber(goodCangResponse.getData().getTrackingNo());
            packageService.save(packageEntity);
        }
        if ("Failure".equalsIgnoreCase(goodCangResponse.getAsk()) && goodCangResponse.getError() != null && "10055".equals(goodCangResponse.getError().getErrCode())) {
            // 该订单被海外仓人员被删除了
            goodCangResponse.setData(new GoodCangResponse.Data());
            packageEntity.setStatus(PackageStatusEnum.CANCEL.getDesc());
            LOGGER.info("{}取消包裹", JsonMapper.toJson(packageEntity));
            packageService.save(packageEntity);
        }
        if ("D".equalsIgnoreCase(goodCangResponse.getData().getOrderStatus()) && PackageStatusEnum.CREATED.getDesc().equals(packageEntity.getStatus())) {
            packageEntity.setStatus(PackageStatusEnum.SHIPPED.getDesc());
            if (StrUtil.isNotBlank(packageEntity.getSecondaryNumber())) {
                packageEntity.setLogisticsNo(packageEntity.getSecondaryNumber());
            }
            packageService.save(packageEntity);
        } else if ("X".equalsIgnoreCase(goodCangResponse.getData().getOrderStatus())) {
            packageEntity.setStatus(PackageStatusEnum.CANCEL.getDesc());
            LOGGER.info("{}取消包裹", JsonMapper.toJson(packageEntity));
            packageService.save(packageEntity);
        }
        SecondaryNumberResponse secondaryNumber = super.getSecondaryNumber(packageEntity);
        secondaryNumber.setPackageWeight(StringUtils.hasText(goodCangResponse.getData().getOrderWeight()) ? new BigDecimal(goodCangResponse.getData().getOrderWeight()) : null);
        secondaryNumber.setLogisticsMethod(packageEntity.getLogisticsMethod());
        secondaryNumber.setLogisticsChannelCode(packageEntity.getLogisticsChannelCode());
        secondaryNumber.setSecondaryNumber(packageEntity.getSecondaryNumber());
        secondaryNumber.setPackageStatus(packageEntity.getStatus());
        return secondaryNumber;
    }

    @Override
    public GenerateOrderResponse doRequest(OrderNewRequest orderRequest, Map<String, String> configMap, String requestContent) {
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(orderRequest.getOrderInfo());
        GenerateOrderResponse response = new GenerateOrderResponse();
        GoodCangResponse goodCangResponse = null;
        try {
            goodCangResponse = postToGoodCang(requestContent, configMap, goodcangServiceURL, GoodCangResponse.class);
            if (goodCangResponse != null && "Success".equalsIgnoreCase(goodCangResponse.getAsk()) && StringUtils.hasText(goodCangResponse.getData() == null ? "" : goodCangResponse.getData().getOrderCode())) {
                if (StrUtil.isAllNotBlank(orderRequest.getOrderInfo().getMappingLogisticsNo(), orderRequest.getOrderInfo().getMappingLogisticsNoLabel())
                        && StrUtil.startWithIgnoreCase(orderRequest.getOrderInfo().getLogisticsChannelCode(), "wh")) {
                    uploadLabel(orderRequest, configMap, goodCangResponse);
                }
                GenerateOrderResponse.SuccessEntity successEntity = buildSuccessEntity(orderRequest.getOrderInfo(), goodCangResponse.getData().getOrderCode(), "", goodCangResponse.getData().getOrderCode(), null);
                response.setSuccessEntity(successEntity);
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, requestContent, JsonMapper.toJson(goodCangResponse), successEntity.getLogisticsNo());
            } else {
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, requestContent, JsonMapper.toJson(goodCangResponse == null ? "服务器错误！" : goodCangResponse), null);
                LOGGER.error("谷仓创建订单失败：request=={}==， response=={}==", requestContent, JsonMapper.toJson(goodCangResponse == null ? "服务器错误！" : goodCangResponse));
                response.setError(buildError("400", judgeResponse(goodCangResponse)));
            }
        } catch (Exception e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, requestContent, goodCangResponse == null ? e.getMessage() : JsonMapper.toJson(goodCangResponse), null);
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return response;
    }

    private void uploadLabel(OrderNewRequest orderRequest, Map<String, String> configMap, GoodCangResponse goodCangResponse) {
        try {
            GoodCangUploadPdfRequest request = new GoodCangUploadPdfRequest();
            request.setOrderCode(goodCangResponse.getData().getOrderCode());
            request.setTrackingNumber(orderRequest.getOrderInfo().getMappingLogisticsNo());
            GoodCangLabelInfo goodCangLabelInfo = new GoodCangLabelInfo();
            goodCangLabelInfo.setLabelUrlList(Collections.singletonList(orderRequest.getOrderInfo().getMappingLogisticsNoLabel()));
            request.setLabelInfo(goodCangLabelInfo);
            GoodCangResponse goodCangResponse1 = postToGoodCang(JsonMapper.toJson(request), configMap, goodcangUploadPdfURL, GoodCangResponse.class);
            if (goodCangResponse1.getError() != null && StrUtil.isNotBlank(goodCangResponse1.getError().getErrMessage())) {
                Map<String, String> map = new HashMap<>();
                map.put("order_code", goodCangResponse.getData().getOrderCode());
                map.put("reason", "订单取消同步");
                postToGoodCang(JsonMapper.toJson(map), configMap, cancelOrder, JSONObject.class);
                throw new InvalidRequestException("谷仓上传面单失败：" + goodCangResponse1.getError().getErrMessage());
            }
        } catch (Exception e) {
            Map<String, String> map = new HashMap<>();
            map.put("order_code", goodCangResponse.getData().getOrderCode());
            map.put("reason", "订单取消同步");
            postToGoodCang(JsonMapper.toJson(map), configMap, cancelOrder, JSONObject.class);
            throw new InvalidRequestException("谷仓上传面单失败", e);
        }

    }

    private <T> T postToGoodCang(String requestContent, Map<String, String> configMap, String goodcangServiceURL, Class<T> responseClass) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON_UTF8));
        headers.add("app-token", configMap.get(APP_TOKEN));
        headers.add("app-key", configMap.get(APP_KEY));
        HttpEntity<String> entity = new HttpEntity<>(requestContent, headers);
        LOGGER.info("{}谷仓请求体", requestContent.replaceAll("Error", "错误"));
        T body = restTemplate.postForEntity(goodcangServiceURL, entity, responseClass).getBody();
        LOGGER.info("{}谷仓返回内容", JsonMapper.toJson(body).replaceAll("Error", "错误"));
        return body;
    }

    private String judgeResponse(GoodCangResponse response) {
        if (response == null) {
            return "创建谷仓订单失败，请联系技术人员";
        }
        List<String> errorMsg = new ArrayList<>();
        if (StringUtils.hasText(response.getMessage())) {
            errorMsg.add(response.getMessage());
        }
        if (response.getError() != null && StringUtils.hasText(response.getError().getErrMessage())) {
            errorMsg.add(response.getError().getErrMessage());
        }
        if (!CollectionUtils.isEmpty(errorMsg)) {
            return errorMsg.stream().distinct().collect(Collectors.joining(";"));
        }
        return "创建谷仓订单失败，请联系技术人员";
    }


    @Override
    protected String buildSerializedRequest(OrderNewRequest request, TmsLogisticsAccountEntity logisticsAccountEntity) {
        Map<String, String> configMap = buildAccountConfig(logisticsAccountEntity, request.getOrderInfo());
        GoodCangOrderRequest request1 = buildLogisticsOrderRequest(request, configMap);
        return JsonMapper.toJson(request1);
    }

    @Override
    public void validateOrderRequest(OrderNewRequest request) {
        OrderNewInfo orderInfo = request.getOrderInfo();
        super.validateOrderRequest(request);
        Validator.isValid(orderInfo.getReceiver(), attr -> !Objects.isNull(attr), "receiver 节点不能为空");
        Validator.isValid(orderInfo.getReceiver().getPostCode(), StringUtils::hasText, "receiver.postCode 节点不能为空");
        Validator.isValid(orderInfo.getReceiver().getStreet(), attr -> attr.length() < 150, "地址长度超过限制");
        orderInfo.getOrderItemInfoList().forEach(item -> {
            Validator.isValid(item.getCount(), attr -> !Objects.isNull(attr), "OrderItemInfo.count 不能为空");
            Validator.isValid(item.getSku(), StringUtils::hasText, "OrderItemInfo.sku 不能为空");
        });
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.labelFolder += "GoodCang/";
        this.ossLabelFolder += "GoodCang/";
    }

    public Map<String, String> getSelfConfigMap(String location, String logisticsChannel) {
        TmsLogisticsChannelConfigEntity channelConfigEntity = channelConfigRepository.findByLogisticsChannelNameAndLocation(logisticsChannel, location);
        List<TmsLogisticsAccountEntity> logisticsAccountEntityList = logisticsAccountRepository.findByLogisticsCompanyAndLocation(channelConfigEntity.getLogisticsCompany(), location);
        if (CollectionUtils.isEmpty(logisticsAccountEntityList)) {
            throw new BusinessServiceException("未配置物流账号");
        }
        return logisticsAccountService.buildAccountConfig(logisticsAccountEntityList.get(0).getId());
    }


    public GoodCangStockInOrderNoResponse createStockInOrder(WmsCreateStockInOrderRequest request1) {
        LOGGER.info("推送谷仓入库单start");
        Map<String, String> configMap = configService.getConfigMap(request1.getSpace());
        CreateStockInOrderRequest goodCangReq = new CreateStockInOrderRequest();
        BeanUtilsEx.copyProperties(request1, goodCangReq);
//        List<TmsLogisticsAccountEntity> logisticsAccountEntityList = logisticsAccountRepository.findByLogisticsCompanyAndStatus("谷仓-美南UPS", StatusRequest.ENABLE);
//        if (CollectionUtils.isEmpty(logisticsAccountEntityList)) {
//            throw new BusinessServiceException("未配置物流账号");
//        }
//        Map<String, String> configMap = logisticsAccountService.buildAccountConfig(logisticsAccountEntityList.get(0).getId());
        goodCangReq.setTransitType(0);
        goodCangReq.setWarehouseCode(configMap.get(WAREHOUSE_CODE_STOCK_IN));
        goodCangReq.setVerify(configMap.get(VERIFY_STOCK_IN));
        goodCangReq.setEtaDate(DateUtil.offsetDay(new Date(), 20));
        if (StrUtil.contains(request1.getLogisticsCompany(), "空")) {
            goodCangReq.setReceivingShippingType(0);
        } else if (StrUtil.contains(request1.getLogisticsCompany(), "海")) {
            goodCangReq.setReceivingShippingType(1);
        } else {
            goodCangReq.setReceivingShippingType(Integer.valueOf(configMap.get(RECEIVING_SHIPPING_TYPE)));
        }
        Map<String, String> spaceSkuMapping = spaceSkuMappingService.findSpaceSkuBySku(request1.getSkuList(), request1.getLocation(), request1.getSpace());
        goodCangReq.setItems(request1.getItems().stream().map(it -> {
            StockInOrderBox box = new StockInOrderBox();
            BeanUtilsEx.copyProperties(it, box);
            box.setBoxDetails(it.getBoxDetails().stream().map(iit -> {
                BoxDetails details = new BoxDetails();
                BeanUtilsEx.copyProperties(iit, details);
                details.setProductSku(spaceSkuMapping.getOrDefault(details.getProductSku(), iit.getProductSku()));
                return details;
            }).collect(Collectors.toList()));
            return box;
        }).collect(Collectors.toList()));
        TmsRequestLogEntity logEntity = requestLogService.recordOtherInfoLog("谷仓创建入库单", "create_grn", goodCangReq.getReferenceNo(), null, "create-stock-in-order");
        String requestJson = JsonMapper.toJson(goodCangReq);
        GoodCangCreateStockInOrderResponse response = null;
        try {
            response = postToGoodCang(requestJson, configMap, goodcangCreateGrn, GoodCangCreateStockInOrderResponse.class);
            if (response != null && "Success".equalsIgnoreCase(response.getAsk()) && StringUtils.hasText(response.getData() == null ? "" : response.getData().getReceivingCode())) {
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, requestJson, JsonMapper.toJson(response), response.getData().getReceivingCode());
                return response.getData();
            } else {
                getErrorMsg(response);
            }
        } catch (Exception e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, requestJson, Objects.isNull(response) ? e.getMessage() : JsonMapper.toJson(response), null);
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        LOGGER.info("推送谷仓入库单end");
        throw new BusinessServiceException("创建谷仓入库单失败");
    }

    @Override
    public StockTransferTrackingResponse getStockInOrderInfo(Map<String, String> configMap, TmsGetStockInOrderInfo req) {
        TmsRequestLogEntity logEntity = requestLogService.recordOtherInfoLog("谷仓查询入库单", "get_grn_detail", req.getStockInReferenceNo(), null, "get-stock-in-order");
        GoodCangStockInOrderInfoRequest request = new GoodCangStockInOrderInfoRequest();
        request.setReceivingCode(req.getStockInReferenceNo());
        String requestJson = JsonMapper.toJson(request);
        GoodCangStockInOrderInfoResponse response = null;
        try {
            response = postToGoodCang(requestJson, configMap, goodcangGetGrnDetail, GoodCangStockInOrderInfoResponse.class);
            if (response != null && "Success".equalsIgnoreCase(response.getAsk()) && response.getData() != null) {
                StockTransferTrackingResponse response1 = new StockTransferTrackingResponse();
                if (response.getData().getReceivingStatus() == null || response.getData().getReceivingStatus() < 8 || response.getData().getReceivingStatus() > 10) {
                    return response1;
                }
                List<String> spaceSkuList = response.getData().getOverseasDetail().stream().map(GoodCangStockInOrderInfo.OverseasDetail::getProductSku).collect(Collectors.toList());
                Map<String, String> spaceSkuMapping = spaceSkuMappingService.findSkuBySpaceSku(spaceSkuList, req.getLocation(), req.getSpace());
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, requestJson, JsonMapper.toJson(response), response.getData().getReceivingCode());
                response1.setTrackingStatusOrigin(response.getData().getReceivingStatus());
                if (Objects.equals(response.getData().getReceivingStatus(), 10)) {
                    response1.setTrackingStatus(StockTransferTrackingStatusEnum.SHELVED.name());
                }
                response1.setTrackingItemList(response.getData().getOverseasDetail().stream().map(detail -> {
                    StockTransferTrackingResponse.StockTrackingItem trackingItem = new StockTransferTrackingResponse.StockTrackingItem();
                    trackingItem.setSku(spaceSkuMapping.getOrDefault(detail.getProductSku(), detail.getProductSku()));
                    trackingItem.setShelvedQty(detail.getOverseasShelvesCount().intValue());
                    trackingItem.setReceivedQty(detail.getOverseasReceivingCount().intValue());
                    return trackingItem;
                }).collect(Collectors.toList()));
                return response1;
            } else {
                getErrorMsg(response);
            }
        } catch (Exception e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, requestJson, Objects.isNull(response) ? e.getMessage() : JsonMapper.toJson(response), null);
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        throw new BusinessServiceException("查询谷仓入库单失败");
    }

    private void getErrorMsg(GoodCangBaseResponse response) {
        if (response == null) {
            throw new BusinessServiceException("谷仓服务器错误，请稍后重试");
        }
        List<String> errorMsg = new ArrayList<>();
        if (StringUtils.hasText(response.getMessage())) {
            errorMsg.add(response.getMessage());
        }
        if (response.getError() != null && StringUtils.hasText(response.getError().getErrMessage())) {
            errorMsg.add(response.getError().getErrMessage());
        }
        if (!CollectionUtils.isEmpty(errorMsg)) {
            throw new BusinessServiceException(errorMsg.stream().distinct().collect(Collectors.joining(";")));
        }
        throw new BusinessServiceException("谷仓服务器错误，请稍后重试");
    }


    public BaseStringResponse getBarcode(String spaceName, String sku) {
        String location = TenantContext.getTenant();
        String spaceSkuBySku = spaceSkuMappingService.findSpaceSkuBySku(sku, location, spaceName);
        Map<String, String> configMap = configService.getConfigMap(spaceName);
        GoodCangSkuBarcodeRequest request = new GoodCangSkuBarcodeRequest();
        request.getProductSkuArr().add(spaceSkuBySku);
        request.setPrintSize(Integer.valueOf(configMap.get(PRINT_SIZE_CODE)));
        String requestJson = JsonMapper.toJson(request);
        GoodCangSkuBarcodeResponse response = postToGoodCang(requestJson, configMap, goodcangGetSkuBarcode, GoodCangSkuBarcodeResponse.class);
        BaseStringResponse response1 = new BaseStringResponse();
        if (response != null && response.getData() != null && StringUtils.hasText(response.getData().getLabelImage())) {
            response1.setValue(response.getData().getLabelImage());
            return response1;
        }
        return response1;
    }

    @Override
    public String printShipmentLabel(Map<String, String> map, OverseaPrintShipmentLabelRequest req) {
        GoodCangShipmentLabelRequest labelRequest = new GoodCangShipmentLabelRequest();
        labelRequest.setReceivingCode(req.getPlatformReferenceNo());
        labelRequest.setReceivingBoxNoArr(req.getBoxCodeList());
        String requestJson = JsonMapper.toJson(labelRequest);
        GoodCangSkuBarcodeResponse response = postToGoodCang(requestJson, map, printGoodCangShipment, GoodCangSkuBarcodeResponse.class);
        if (response != null && response.getData() != null && StringUtils.hasText(response.getData().getLabelImage())) {
            return response.getData().getLabelImage();
        }
        throw new BusinessServiceException("无法获取到面单！");
    }

    @Override
    public void cancelOrder(TmsPackageEntity packageEntity) {
        if (StrUtil.equals(packageEntity.getStatus(), PackageStatusEnum.CANCEL.getDesc())) {
            return;
        }
        Map<String, String> configMap;
        if (StrUtil.isNotBlank(packageEntity.getKeyGroup())) {
            configMap = configService.getConfigMap(packageEntity.getKeyGroup());
        } else {
            LOGGER.error("海外仓获取配置异常：使用旧账号配置获取海外仓订单！");
            configMap = logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());
        }
        Map<String, String> map = new HashMap<>();
        map.put("order_code", packageEntity.getLogisticsTid());
        map.put("reason", "订单取消同步");
        LOGGER.info("系统取消出库单{}", packageEntity.getLogisticsNo());
        JSONObject response = postToGoodCang(JsonMapper.toJson(map), configMap, cancelOrder, JSONObject.class);
        if (response == null) {
            throw new BusinessServiceException("谷仓拦截失败！");
        }
        Integer cancelStatus = response.getInt("cancel_status");
        if (cancelStatus != null && cancelStatus == 2) {
            super.cancelOrder(packageEntity);
            return;
        }
        throw new BusinessServiceException("谷仓拦截失败！");
    }

    @Override
    public List<SkuDTO> getOverseaSpaceProductStock(Map<String, String> map, OverseaStockRequest request) {
        List<SkuDTO> result = new ArrayList<>();
        request.setPageSize(50);
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("warehouse_code", map.get(WAREHOUSE_CODE));
        paramsMap.put("pageSize", request.getPageSize());
        paramsMap.put("page", request.getPageIndex());
        if (!CollectionUtils.isEmpty(request.getSkuList())) {
            Map<String, String> spaceSkuBySku = spaceSkuMappingService.findSpaceSkuBySku(request.getSkuList(), request.getLocation(), request.getSpaceName());
            paramsMap.put("product_sku_arr", CollectionUtil.newArrayList(CollectionUtils.isEmpty(spaceSkuBySku) ? request.getSkuList() : spaceSkuBySku.values()));
            GoodCangGetStockResponse response = postToGoodCang(JsonMapper.toJson(paramsMap), map, getStock, GoodCangGetStockResponse.class);
            if (response == null || CollectionUtils.isEmpty(response.getData())) {
                return super.getOverseaSpaceProductStock(map, request);
            }
            Map<String, String> mapping = spaceSkuMappingService.findAllSkuMapping(request.getLocation(), request.getSpaceName());
            buildSkuDto(result, response, mapping);
            return result;
        }
        GoodCangGetStockResponse response = postToGoodCang(JsonMapper.toJson(paramsMap), map, getStock, GoodCangGetStockResponse.class);
        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            return super.getOverseaSpaceProductStock(map, request);
        }
        Map<String, String> mapping = spaceSkuMappingService.findAllSkuMapping(request.getLocation(), request.getSpaceName());
        buildSkuDto(result, response, mapping);
        while (result.size() < response.getCount()) {
            int pageNum = (int) paramsMap.get("page");
            paramsMap.put("page", pageNum + 1);
            GoodCangGetStockResponse responseFor = postToGoodCang(JsonMapper.toJson(paramsMap), map, getStock, GoodCangGetStockResponse.class);
            if (responseFor == null || CollectionUtils.isEmpty(responseFor.getData())) {
                break;
            }
            buildSkuDto(result, responseFor, mapping);
        }
        return result;
    }

    private void buildSkuDto(List<SkuDTO> result, GoodCangGetStockResponse infoResponse, Map<String, String> mapping) {
        infoResponse.getData().forEach(item -> {
            SkuDTO dto = new SkuDTO();
            dto.setSku(mapping.getOrDefault(item.getProductSku(), item.getProductSku()));
            dto.setQty(item.getSellable() + item.getReserved());
            result.add(dto);
        });
    }
}
