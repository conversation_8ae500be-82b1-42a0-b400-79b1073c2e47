package com.nsy.api.tms.service.external.refactor;

import cn.hutool.core.util.StrUtil;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.annotation.LogisticsServiceHandler;
import com.nsy.api.tms.constants.LogisticsChannelConstant;
import com.nsy.api.tms.dao.entity.BdShipperAddressEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsAccountEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.domain.Address;
import com.nsy.api.tms.domain.CaiNiaoInfo;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.domain.order.OrderNewInfo;
import com.nsy.api.tms.domain.order.StockoutShipmentTmsModel;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.logistics.cainiao.CainiaoTmsGetRequest;
import com.nsy.api.tms.request.OrderNewRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.utils.JsonMapper;
import com.nsy.api.tms.utils.Validator;
import com.taobao.pac.sdk.cp.dataobject.request.TMS_WAYBILL_GET.AddressDto;
import com.taobao.pac.sdk.cp.dataobject.request.TMS_WAYBILL_GET.Item;
import com.taobao.pac.sdk.cp.dataobject.request.TMS_WAYBILL_GET.OrderInfoDto;
import com.taobao.pac.sdk.cp.dataobject.request.TMS_WAYBILL_GET.PackageInfoDto;
import com.taobao.pac.sdk.cp.dataobject.request.TMS_WAYBILL_GET.TmsWaybillGetRequest;
import com.taobao.pac.sdk.cp.dataobject.request.TMS_WAYBILL_GET.TradeOrderInfoDto;
import com.taobao.pac.sdk.cp.dataobject.request.TMS_WAYBILL_GET.UserInfoDto;
import com.taobao.pac.sdk.cp.dataobject.response.TMS_WAYBILL_GET.TmsWaybillGetResponse;
import com.taobao.pac.sdk.cp.dataobject.response.TMS_WAYBILL_GET.WaybillCloudPrintResponse;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;


@Service
@LogisticsServiceHandler(logisticsMethod = LogisticsMethodEnum.CAINIAO)
public class CainiaoNewService extends BaseLogisticsNewService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CainiaoNewService.class);

    public static final String CONFIG_APP_KEY = "cainiao_appKey";
    public static final String CONFIG_APP_SECRET = "cainiao_appSecret";
    public static final String CONFIG_TOKEN = "cainiao_token";

    @Value("${cainiao.server.url}")
    private String serverUrl;

    //作为唯一标识
    String objectId;

    public void preDeal(OrderNewRequest request) {
        setLogisticsInfo(request.getOrderInfo());
        setCaiNiaoInfo(request.getOrderInfo());
    }

    @Override
    public GenerateOrderResponse syncGenerateOrder(OrderNewRequest request, TmsLogisticsAccountEntity logisticsAccountEntity) {
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(request.getOrderInfo());
        GenerateOrderResponse generateOrderResponse = new GenerateOrderResponse();
        CainiaoTmsGetRequest cainiaoTmsGetRequest = null;
        try {
            Map<String, String> configMap = buildAccountConfig(logisticsAccountEntity, request.getOrderInfo());
            cainiaoTmsGetRequest = buildLogisticsOrderRequest(request, configMap);

            FormBody.Builder formBodyBuilder = new FormBody.Builder();
            formBodyBuilder.add("logistic_provider_id", configMap.get(CONFIG_TOKEN));
            formBodyBuilder.add("msg_type", "TMS_WAYBILL_GET");
            formBodyBuilder.add("logistics_interface", JsonMapper.toJson(cainiaoTmsGetRequest.getTmsWaybillGetRequest()));
            formBodyBuilder.add("data_digest", getDigest(JsonMapper.toJson(cainiaoTmsGetRequest.getTmsWaybillGetRequest()), configMap.get(CONFIG_APP_SECRET)));

            Request httpRequest = new Request.Builder().url(serverUrl).post(formBodyBuilder.build()).build();
            Response response = new OkHttpClient().newCall(httpRequest).execute();
            LOGGER.info("http post for cainiao result, request data is==={}===, response data is==={}===", JSONUtils.toJSON(httpRequest), JSONUtils.toJSON(response));
            TmsWaybillGetResponse tmsWaybillGetResponse = JsonMapper.fromJson(response.body().string(), TmsWaybillGetResponse.class);
            if (!tmsWaybillGetResponse.isSuccess()) {
                //如果调用失败
                GenerateOrderResponse.Error error = parseFailedResponse(cainiaoTmsGetRequest, tmsWaybillGetResponse, logEntity);
                generateOrderResponse.setError(error);
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, JsonMapper.toJson(cainiaoTmsGetRequest), JsonMapper.toJson(tmsWaybillGetResponse), null);
            } else {
                //如果调用成功
                GenerateOrderResponse.SuccessEntity successEntity = parseSuccessResponse(request.getOrderInfo(), cainiaoTmsGetRequest, tmsWaybillGetResponse, logEntity);
                generateOrderResponse.setSuccessEntity(successEntity);
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, JsonMapper.toJson(cainiaoTmsGetRequest), JsonMapper.toJson(tmsWaybillGetResponse), successEntity.getLogisticsNo());
            }
        } catch (Exception e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, JsonMapper.toJson(cainiaoTmsGetRequest), e.getMessage(), null);
            throw new RuntimeException(e);
        }
        return generateOrderResponse;
    }

    /**
     * 构建对接菜鸟获取面单服务所需的请求参数
     *
     * @param request
     * @return
     */
    protected CainiaoTmsGetRequest buildLogisticsOrderRequest(OrderNewRequest request, Map<String, String> configMap) {
        CainiaoTmsGetRequest cainiaoTmsGetRequest = new CainiaoTmsGetRequest();
        TmsWaybillGetRequest tmsWaybillGetRequest = new TmsWaybillGetRequest();
        cainiaoTmsGetRequest.setTmsWaybillGetRequest(tmsWaybillGetRequest);
        OrderNewInfo orderInfo = request.getOrderInfo();
        Address orderReceiver = orderInfo.getReceiver();
        Address orderSender = orderInfo.getSender();
        //配送公司编码
        tmsWaybillGetRequest.setCpCode(orderInfo.getLogisticsChannelCode());
        if ("SF".equalsIgnoreCase(orderInfo.getLogisticsChannelCode())) {
            tmsWaybillGetRequest.setBrandCode("SF");
            if (StrUtil.isNotBlank(orderInfo.getCaiNiaoInfo().getLogisticsServices())) {
                tmsWaybillGetRequest.setExtraInfo(orderInfo.getCaiNiaoInfo().getLogisticsServices());
            }
        }
        BdShipperAddressEntity addressEntity = logisticsAccountService.getLogisticsAccount(orderInfo);
        //发件人信息
        UserInfoDto sender = new UserInfoDto();
        tmsWaybillGetRequest.setSender(sender);
        setSendInfo(orderSender, sender, addressEntity);

        //寄件地址，这里的地址需要是卖家订购电子面单服务时使用的订购地址，具体可以通过TMS_WAYBILL_SUBSCRIPTION_QUERY接口获取
        AddressDto sendAddress = new AddressDto();
        sender.setAddress(sendAddress);
        setSendAddressInfo(sendAddress, addressEntity);

        //请求ID,为保证唯一性的标识
        objectId = UUID.randomUUID().toString();
        //收件信息
        List<TradeOrderInfoDto> tradeOrderInfoDtos = new ArrayList<>();
        tmsWaybillGetRequest.setTradeOrderInfoDtos(tradeOrderInfoDtos);

        TradeOrderInfoDto tradeOrderInfoDto = new TradeOrderInfoDto();
        tradeOrderInfoDto.setObjectId(objectId);
        if (!"SF".equalsIgnoreCase(orderInfo.getLogisticsChannelCode())) {
            tradeOrderInfoDto.setLogisticsServices(orderInfo.getCaiNiaoInfo() == null ? null : orderInfo.getCaiNiaoInfo().getLogisticsServices());
        }
        //打印模板
        String templateUrl = getTemplateUrl(orderInfo.getLogisticsChannelCode());
        //打印模板的url
        tradeOrderInfoDto.setTemplateUrl(templateUrl);
        tradeOrderInfoDtos.add(tradeOrderInfoDto);

        OrderInfoDto orderInfoDto = new OrderInfoDto();
        tradeOrderInfoDto.setOrderInfo(orderInfoDto);

        //订单渠道平台编码
        String orderChannelsType = getOrderChannelsType(orderInfo.getPlatform());
        orderInfoDto.setOrderChannelsType(orderChannelsType);
        List<String> orderList = new ArrayList<String>();
        orderInfoDto.setTradeOrderList(orderList);

        //订单列表，这里的场景是一个订单获取一个面单号
        orderList.add(orderInfo.getTmsTid() + generateRandom(3));

        //包裹信息
        setPackageInfo(tradeOrderInfoDto, orderInfo);

        //收件人信息
        UserInfoDto receiver = new UserInfoDto();
        tradeOrderInfoDto.setRecipient(receiver);
        setReciverInfo(receiver, orderReceiver);
        //收件人地址
        AddressDto receiveAddress = new AddressDto();
        receiver.setAddress(receiveAddress);
        setReceiveAddressInfo(receiveAddress, orderReceiver);

        return cainiaoTmsGetRequest;
    }

    private void setPackageInfo(TradeOrderInfoDto tradeOrderInfoDto, OrderNewInfo orderInfo) {
        PackageInfoDto packageInfoDto = new PackageInfoDto();
        tradeOrderInfoDto.setPackageInfo(packageInfoDto);
        List<Item> items = new ArrayList<Item>();
        packageInfoDto.setItems(items);
        packageInfoDto.setVolume(Math.round(orderInfo.getLength() * orderInfo.getWidth() * orderInfo.getHeight()));
        packageInfoDto.setWeight(Math.round(orderInfo.getWeight()));
        if (orderInfo.getBoxCount() > 0) {
            packageInfoDto.setTotalPackagesCount(orderInfo.getBoxCount());
            packageInfoDto.setGoodsDescription(String.valueOf(orderInfo.getBoxCount()) + "*");
        }
        orderInfo.getOrderItemInfoList().forEach(orderItemInfo -> {
            Item item = new Item();
            item.setName(org.apache.commons.lang3.StringUtils.substring(orderItemInfo.getCnName(), 0, 127));
            item.setCount(orderItemInfo.getCount());
            items.add(item);
        });
    }

    private void setReceiveAddressInfo(AddressDto receiveAddress, Address orderReceiver) {
        String province = orderReceiver.getProvince();
        String city = orderReceiver.getCity();
        String county = orderReceiver.getCounty();
        String street = orderReceiver.getStreet();

        // 验证地址字段是否包含中文字符
        validateAddressContainsChinese(province, city, county, street);

        receiveAddress.setProvince(province);
        receiveAddress.setCity(city);
        receiveAddress.setDistrict(county);
        receiveAddress.setDetail(street);
    }

    /**
     * 验证地址字段是否包含中文字符，如果所有字段都不包含中文字符则抛出异常
     *
     * @param province 省份
     * @param city 城市
     * @param county 区县
     * @param street 街道详细地址
     */
    private void validateAddressContainsChinese(String province, String city, String county, String street) {
        boolean hasChineseInProvince = containsChinese(province);
        boolean hasChineseInCity = containsChinese(city);
        boolean hasChineseInCounty = containsChinese(county);
        boolean hasChineseInStreet = containsChinese(street);
        // 如果所有地址字段都不包含中文字符，则抛出异常
        if (!hasChineseInProvince && !hasChineseInCity && !hasChineseInCounty && !hasChineseInStreet) {
            throw new RuntimeException("收件地址信息不能全部为英文字符，必须包含中文字符");
        }
    }

    /**
     * 检测字符串是否包含中文字符
     *
     * @param text 待检测的字符串
     * @return true表示包含中文字符，false表示不包含中文字符
     */
    private boolean containsChinese(String text) {
        if (StrUtil.isBlank(text)) {
            return false;
        }

        // 中文字符的Unicode范围：\u4e00-\u9fff
        for (char c : text.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fff) {
                return true;
            }
        }
        return false;
    }

    private void setReciverInfo(UserInfoDto receiver, Address orderReceiver) {
        //收件人姓名
        receiver.setName(orderReceiver.getName());
        //收件人移动电话
        receiver.setMobile(orderReceiver.getMobile());
        receiver.setPhone(orderReceiver.getPhone());
        if (!StringUtils.hasText(receiver.getMobile()) && !StringUtils.hasText(receiver.getPhone())) {
            receiver.setPhone("1234567890");
            receiver.setMobile("1234567890");
        }
    }

    private void setSendAddressInfo(AddressDto sendAddress, BdShipperAddressEntity addressEntity) {
        sendAddress.setProvince(addressEntity.getShipperState());
        sendAddress.setCity(addressEntity.getShipperCity());
        sendAddress.setDistrict(addressEntity.getShipperDistrict());
        sendAddress.setDetail(addressEntity.getShipperAddress());
    }

    private void setSendInfo(Address orderSender, UserInfoDto sender, BdShipperAddressEntity addressEntity) {
        //发件人姓名
        sender.setName(StringUtils.hasText(orderSender.getName()) ? orderSender.getName() : addressEntity.getShipperName());
        //发件人固定电话
        sender.setPhone(StringUtils.hasText(orderSender.getPhone()) ? orderSender.getPhone() : orderSender.getMobile());
        //发件人手机号
        sender.setMobile(StringUtils.hasText(orderSender.getMobile()) ? orderSender.getMobile() : orderSender.getPhone());
        if (!StringUtils.hasText(sender.getPhone())) {
            sender.setPhone(addressEntity.getShipperPhone());
        }
        if (!StringUtils.hasText(sender.getMobile())) {
            sender.setMobile(addressEntity.getShipperMobile());
        }
    }

    private String getDigest(String apiContent, String secretKey) throws UnsupportedEncodingException, NoSuchAlgorithmException {
        String combine = apiContent + secretKey;
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(combine.getBytes("UTF-8"));
        return new String(Base64.encodeBase64(md.digest()), "UTF-8");
    }

    /**
     * 返回调用成功的结果解析
     *
     * @param response
     * @param request
     * @param orderInfo
     * @return
     */
    private GenerateOrderResponse.SuccessEntity parseSuccessResponse(OrderNewInfo orderInfo, CainiaoTmsGetRequest request, TmsWaybillGetResponse response, TmsRequestLogEntity logEntity) {
        GenerateOrderResponse.SuccessEntity success = new GenerateOrderResponse.SuccessEntity();
        List<WaybillCloudPrintResponse> waybillCloudPrintResponseList = response.getWaybillCloudPrintResponseList();
        for (WaybillCloudPrintResponse waybillCloudPrintResponse : waybillCloudPrintResponseList) {
            if (objectId.equals(waybillCloudPrintResponse.getObjectId())) {

                String wayBillCode = waybillCloudPrintResponse.getParentWaybillCode();
                //面单号
                if (waybillCloudPrintResponse.getParentWaybillCode() == null || waybillCloudPrintResponse.getParentWaybillCode().isEmpty()) {
                    wayBillCode = waybillCloudPrintResponse.getWaybillCode();
                }
                success.setLogisticsNo(wayBillCode);
                //打印数据
                String printData = waybillCloudPrintResponse.getPrintData();
                success.setPrintData(printData);
                success.setObjectId(waybillCloudPrintResponse.getObjectId());

                //数据库里插入package
                //TODO track 菜鸟物流暂时不进行物流追踪
                TmsPackageEntity packageEntity = packageService.buildBaseTmsPackageEntityBuilder(wayBillCode, orderInfo)
                        .printData(printData).trackIgnore(1).build();
                LOGGER.info("insert into tms_package entity is:{}", JSONUtils.toJSON(packageEntity));
                TmsPackageEntity tmsPackageEntity = packageService.save(packageEntity);
                success.setId(tmsPackageEntity.getId());
            }
        }
        requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, JsonMapper.toJson(request), JsonMapper.toJson(response), success.getLogisticsNo());
        return success;
    }

    /**
     * 解析返回调用失败结果的解析
     *
     * @param response
     * @param cainiaoTmsGetRequest
     * @return
     */
    private GenerateOrderResponse.Error parseFailedResponse(CainiaoTmsGetRequest cainiaoTmsGetRequest, TmsWaybillGetResponse response, TmsRequestLogEntity logEntity) {
        GenerateOrderResponse.Error error = new GenerateOrderResponse.Error();
        error.setMessage(response.getErrorMsg());
        error.setCode(response.getErrorCode());
        //记录调用日志
        requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, JsonMapper.toJson(cainiaoTmsGetRequest), JsonMapper.toJson(response), null);
        return error;
    }

    private static Map<String, String> caiNiaoPrintTempletes() {
        Map<String, String> caiNiaoPrintTempletes = new HashMap<>();
        caiNiaoPrintTempletes.put("ZJS", "http://cloudprint.cainiao.com/template/standard/901/110"); //宅急送
        caiNiaoPrintTempletes.put("YUNDA", "http://cloudprint.cainiao.com/template/standard/288658/14"); //韵达
        caiNiaoPrintTempletes.put("GTO", "http://cloudprint.cainiao.com/template/standard/1101/98"); //国通
        caiNiaoPrintTempletes.put("DBKD", "http://cloudprint.cainiao.com/template/standard/1301/92"); //德邦快递
        caiNiaoPrintTempletes.put("EMS", "http://cloudprint.cainiao.com/template/standard/701/136"); //EMS标准快递
        caiNiaoPrintTempletes.put("QFKD", "http://cloudprint.cainiao.com/template/standard/1401/110"); //全峰
        caiNiaoPrintTempletes.put("UC", "http://cloudprint.cainiao.com/template/standard/1001/132"); //优速
        caiNiaoPrintTempletes.put("POSTB", "http://cloudprint.cainiao.com/template/standard/801/120"); //邮政快递包裹
        caiNiaoPrintTempletes.put("EYB", "http://cloudprint.cainiao.com/template/standard/1601/110"); //EMS经济快件
        caiNiaoPrintTempletes.put("STO", "http://cloudprint.cainiao.com/template/standard/201/160"); //申通
        caiNiaoPrintTempletes.put("SURE", "http://cloudprint.cainiao.com/template/standard/281280/3"); //速尔
        // caiNiaoPrintTempletes.put("SF", "http://cloudprint.cainiao.com/template/standard/1501/72"); // 顺丰 100 * 150
        caiNiaoPrintTempletes.put("SF", "http://cloudprint.cainiao.com/template/standard/474941/24"); //顺丰 76*130模板
        caiNiaoPrintTempletes.put("ZTO", "http://cloudprint.cainiao.com/template/standard/300336/39"); //中通
        caiNiaoPrintTempletes.put("YTO", "http://cloudprint.cainiao.com/template/standard/290659/14"); //圆通
        caiNiaoPrintTempletes.put("TTKDEX", "http://cloudprint.cainiao.com/template/standard/601/136"); //天天
        caiNiaoPrintTempletes.put("FAST", "http://cloudprint.cainiao.com/template/standard/1201/98"); //快捷
        caiNiaoPrintTempletes.put("BESTQJT", "http://cloudprint.cainiao.com/template/standard/83910/69"); //百世快运
        caiNiaoPrintTempletes.put("5000000007756", "http://cloudprint.cainiao.com/template/standard/82710/15"); //邮政标准快递
        caiNiaoPrintTempletes.put("2608021499_235", "http://cloudprint.cainiao.com/template/standard/183119"); //安能
        caiNiaoPrintTempletes.put("CN7000001003751", "http://cloudprint.cainiao.com/template/standard/182004/9"); //跨越三联单
        caiNiaoPrintTempletes.put("HTKY", "http://cloudprint.cainiao.com/template/standard/278716/53"); //极兔速递一联单
        return caiNiaoPrintTempletes;
    }

    private String getTemplateUrl(String cpCode) {
        String templateUrl = "";
        if (caiNiaoPrintTempletes().containsKey(cpCode)) {
            templateUrl = caiNiaoPrintTempletes().get(cpCode);
        }
        return templateUrl;
    }

    private static Map<String, String> caiNiaoOrderChannelsTypes() {
        Map<String, String> caiNiaoOrderChannelsTypes = new HashMap();
        //淘宝
        caiNiaoOrderChannelsTypes.put("TaoBao", "TB");
        //caiNiaoOrderChannelsTypes.Add("", "TM");//天猫
        //京东
        caiNiaoOrderChannelsTypes.put("JingDong", "JD");
        //当当
        caiNiaoOrderChannelsTypes.put("DangDang", "DD");
        //拍拍
        caiNiaoOrderChannelsTypes.put("PaiPai", "PP");
        //caiNiaoOrderChannelsTypes.Add("", "YX");//易讯
        //ebay
        caiNiaoOrderChannelsTypes.put("EBAY", "EBAY");
        //QQ网购
        caiNiaoOrderChannelsTypes.put("QQ", "QQ");
        //亚马逊
        caiNiaoOrderChannelsTypes.put("AMAZON", "AMAZON");
        //苏宁
        caiNiaoOrderChannelsTypes.put("SuNing", "SN");
        //caiNiaoOrderChannelsTypes.Add("", "GM");//国美
        //唯品会
        caiNiaoOrderChannelsTypes.put("WeiPingHui", "WPH");
        //聚美
        caiNiaoOrderChannelsTypes.put("JuMei", "JM");

        //1号店
        caiNiaoOrderChannelsTypes.put("YiHaoDian", "YHD");
        //caiNiaoOrderChannelsTypes.Add("", "VANCL");//凡客
        //邮乐
        caiNiaoOrderChannelsTypes.put("YouLe", "YL");
        //优购
        caiNiaoOrderChannelsTypes.put("YouGou", "YG");
        //阿里 巴巴
        caiNiaoOrderChannelsTypes.put("AliBaBa", "1688");
        //caiNiaoOrderChannelsTypes.Add("", "OTHERS");
        //caiNiaoOrderChannelsTypes.Add("", "LF");//乐蜂
        //caiNiaoOrderChannelsTypes.Add("", "MGJ");//蘑菇街
        //caiNiaoOrderChannelsTypes.Add("", "JS");//聚尚
        //caiNiaoOrderChannelsTypes.Add("", "PX");//拍鞋
        //caiNiaoOrderChannelsTypes.Add("", "YT");//银泰
        return caiNiaoOrderChannelsTypes;
    }

    private String getOrderChannelsType(String platformName) {
        String orderChannersType = "OTHERS";
        if (caiNiaoOrderChannelsTypes().containsKey(platformName)) {
            orderChannersType = caiNiaoOrderChannelsTypes().get(platformName);
        }
        return orderChannersType;
    }

    public void validateOrderRequest(OrderNewRequest request) {
        OrderNewInfo orderInfo = request.getOrderInfo();
        super.validateOrderRequest(request);
        Validator.isValid(orderInfo.getPlatform(), Objects::nonNull, "orderInfo.platform 节点不能为空");
        Validator.isValid(orderInfo.getOrderItemInfoList(), attr -> !attr.isEmpty(), "OrderItemList不能为空");
        Validator.isValid(orderInfo.getTmsTid(), Objects::nonNull, "tmsTid不能为空");

        orderInfo.getOrderItemInfoList().forEach(item -> {
            Validator.isValid(item.getCount(), attr -> !Objects.isNull(attr), "OrderItemInfo.count 不能为空");
            Validator.isValid(item.getCnName(), attr -> !Objects.isNull(attr), "OrderItemInfo.CnName 不能为空");
        });

    }

    public void setLogisticsInfo(OrderNewInfo orderNewInfo) {
        orderNewInfo.setReceiveCountryCode("CN");
        orderNewInfo.getReceiver().setCountry("CN");
    }

    public void setCaiNiaoInfo(OrderNewInfo orderInfo) {
        CaiNiaoInfo caiNiaoInfo = new CaiNiaoInfo();
        // 顺丰/速尔  ==>设置到付/月结
        if (Objects.equals(orderInfo.getLogisticsChannelName(), LogisticsChannelConstant.SF)) {
            for (OrderItemInfo item : orderInfo.getOrderItemInfoList()) {
                if (org.springframework.util.StringUtils.hasText(item.getSellerMemo()) && item.getSellerMemo().contains("到付") || org.springframework.util.StringUtils.hasText(item.getSpaceMemo()) && item.getSpaceMemo().contains("到付")) {
                    caiNiaoInfo.setLogisticsServices("{\"payMethod\": 2}");
                    break;
                }
            }
        } else if (Objects.equals(orderInfo.getLogisticsCompany(), LogisticsChannelConstant.SU_ER)) {
            caiNiaoInfo.setLogisticsServices("{\"PAYMENT-TYPE\": {\"value\": \"SENDER-MONTHLY-PAY\"},\"SVC-TRANSPORT-TYPE\": {\"value\": \"VEHICLE\"},\"SVC-DELIVERY-TYPE\": {\"value\": \"SEND\"},\"SVC-DELIVERY-RECEIPT\": {\"value\": \"\"}}");
            for (OrderItemInfo item : orderInfo.getOrderItemInfoList()) {
                if (org.springframework.util.StringUtils.hasText(item.getSellerMemo()) && item.getSellerMemo().contains("到付") || org.springframework.util.StringUtils.hasText(item.getSpaceMemo()) && item.getSpaceMemo().contains("到付")) {
                    caiNiaoInfo.setLogisticsServices("{\"PAYMENT-TYPE\": {\"value\": \"RECEIVER-PAY\"},\"SVC-TRANSPORT-TYPE\": {\"value\": \"VEHICLE\"},\"SVC-DELIVERY-TYPE\": {\"value\": \"SEND\"},\"SVC-DELIVERY-RECEIPT\": {\"value\": \"\"}}");
                    break;
                }
            }
        }
        orderInfo.setCaiNiaoInfo(caiNiaoInfo);
        if (!CollectionUtils.isEmpty(orderInfo.getShipmentTmsModelList())) {
            long count = orderInfo.getShipmentTmsModelList().stream().map(StockoutShipmentTmsModel::getShipmentId).distinct().count();
            orderInfo.setBoxCount(count);
        }

    }

}
