package com.nsy.api.tms.service.external.refactor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.core.apicore.util.MD5Util;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.annotation.LogisticsServiceHandler;
import com.nsy.api.tms.constants.CountryCodeConstant;
import com.nsy.api.tms.dao.entity.TmsLogisticsAccountEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.domain.Address;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.domain.order.OrderNewInfo;
import com.nsy.api.tms.enumeration.LocationEnum;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.external.user.UserDictionaryApiService;
import com.nsy.api.tms.logistics.cne.CneLogisticsRequest;
import com.nsy.api.tms.logistics.cne.CneOrder;
import com.nsy.api.tms.logistics.cne.CneResponse;
import com.nsy.api.tms.logistics.cne.Item;
import com.nsy.api.tms.request.OrderNewRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.response.PrintLabelResponse;
import com.nsy.api.tms.utils.FileUtils;
import com.nsy.api.tms.utils.JsonMapper;
import com.nsy.api.tms.utils.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@LogisticsServiceHandler(logisticsMethod = LogisticsMethodEnum.CNE)
public class CneNewService extends BaseLogisticsNewService implements InitializingBean {
    private static final Logger LOGGER = LoggerFactory.getLogger(CneNewService.class);

    public static final String LABEL_NAME = "%sCNE-%s";
    public static final String CONFIG_APP_KEY = "cne_appKey";
    public static final String CONFIG_SECRET = "cne_appSecret";

    @Autowired
    CneTrackNewService trackService;

    @Resource
    UserDictionaryApiService dictionaryApiService;

    @Value("${cne.order.url}")
    String cneOrderUrl;
    @Value("${cne.label.url}")
    String cneLabelUrl;

    @Override
    public void preDeal(OrderNewRequest request) {
        if (StrUtil.equalsAnyIgnoreCase(request.getOrderInfo().getLocation(), LocationEnum.QUANZHOU.name(), LocationEnum.XIAMEN.name())
                && (StrUtil.equalsAnyIgnoreCase(request.getOrderInfo().getReceiveCountryCode(), CountryCodeConstant.US)
                || StrUtil.equalsAnyIgnoreCase(request.getOrderInfo().getReceiver().getCountry(), CountryCodeConstant.US))) {
            throw new BusinessServiceException("美国不能走CNE");
        }
        reSetUserPriceByTotalNum(request.getOrderInfo().getOrderItemInfoList(), request);
        setLogisticsInfo(request);
    }

    @Override
    public GenerateOrderResponse syncGenerateOrder(OrderNewRequest request, TmsLogisticsAccountEntity logisticsAccountEntity) {
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(request.getOrderInfo());
        GenerateOrderResponse response = new GenerateOrderResponse();
        Map<String, String> configMap = buildAccountConfig(logisticsAccountEntity, request.getOrderInfo());
        CneLogisticsRequest cneLogisticsRequest = buildLogisticsOrderRequest(request, configMap);
        String requestInfo = JSON.toJSONString(cneLogisticsRequest);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity<String> entity = new HttpEntity<>(requestInfo, httpHeaders);
        try {
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(cneOrderUrl, entity, String.class);
            LOGGER.info("requestInfo: {}, responseInfo;{}", requestInfo, responseEntity.getBody());
            CneResponse cneResponse = JSON.parseObject(responseEntity.getBody(), CneResponse.class);
            if (cneResponse.getReturnValue() >= 0 && !StringUtils.hasText(cneResponse.getErrList().get(0).getcMess())) {
                GenerateOrderResponse.SuccessEntity successEntity = processSuccessReply(request.getOrderInfo(), cneResponse, configMap);
                response.setSuccessEntity(successEntity);
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, requestInfo, JsonMapper.toJson(cneResponse), successEntity.getLogisticsNo());
            } else {
                String message = !StringUtils.hasText(cneResponse.getcMess()) ? cneResponse.getErrList().get(0).getcMess() : cneResponse.getcMess();
                response.setError(buildError(cneResponse.getReturnValue().toString(), message));
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, requestInfo, JsonMapper.toJson(cneResponse), null);
            }
        } catch (Exception e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, requestInfo, e.getMessage(), null);
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return response;
    }

    @Override
    public PrintLabelResponse printLabel(TmsPackageEntity packageEntity) {
        if (StringUtils.hasText(packageEntity.getLabelUrl())) {
            return super.printLabel(packageEntity);
        }
        Map<String, String> configMap = logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());
        String labelUrl = getLabelFile(packageEntity.getLogisticsNo(), configMap, packageEntity.getTid());
        packageEntity.setLabelUrl(labelUrl);
        packageService.save(packageEntity);
        LOGGER.info("CNE实时获取面单：{}", JSONUtils.toJSON(packageEntity));
        return super.printLabel(packageEntity);
    }

    @Override
    protected CneLogisticsRequest buildLogisticsOrderRequest(OrderNewRequest orderRequest, Map<String, String> configMap) {
        OrderNewInfo orderInfo = orderRequest.getOrderInfo();
        // cne先设置IOSS
//        buildIoss(orderInfo);
        CneLogisticsRequest request = new CneLogisticsRequest();
        request.setRequestName("PreInputSet");
        long time = new Date().getTime();
        request.setMd5(configMap, time);
        request.setTimeStamp(time);
        request.setCustomerId(configMap.get(CONFIG_APP_KEY));
        request.setOrderList(buildCneOrderList(orderInfo, orderInfo.getReceiver()));
        return request;
    }

//    private void buildIoss(OrderNewInfo orderInfo) {
//        if (!CountryCodeConstant.EUROPEAN_UNION_COUNTRY_CODE.contains(orderInfo.getReceiveCountryCode()) || StringUtils.hasText(orderInfo.getIossNumber())) {
//            return;
//        }
//        orderInfo.setIossNumber("IM4420001201");
//    }

    private List<CneOrder> buildCneOrderList(OrderNewInfo orderInfo, Address receiver) {
        List<CneOrder> cneOrderList = new ArrayList<>();
        CneOrder cneOrder = new CneOrder();
        cneOrder.setPreRecordId(0);
        cneOrder.setExpressType(1); // 快件类型，默认为 1。取值为：0(文件),1(包裹),2(防水袋)
        cneOrder.setOrderId(orderInfo.getTmsTid());
        cneOrder.setcDes(receiver.getCountry());
        cneOrder.setcEmsKind(orderInfo.getLogisticsChannelCode());
        cneOrder.setWeight(orderInfo.getWeight());
        if (StrUtil.isNotBlank(receiver.getTaxNumber())) {
            cneOrder.setVatCode(receiver.getTaxNumber());
        }
        buildReceiver(receiver, cneOrder);
        String currencyCode = null;
        if (StringUtils.hasText(orderInfo.getIossNumber())) {
            cneOrder.setIossCode(orderInfo.getIossNumber());
            currencyCode = "EUR";
        }
        cneOrder.setGoodsList(buildGoodsList(orderInfo.getOrderItemInfoList(), currencyCode));
        cneOrderList.add(cneOrder);
        return cneOrderList;
    }

    private List<Item> buildGoodsList(List<OrderItemInfo> orderItemInfoList, String currencyCode) {
        List<Item> itemList = new ArrayList<>();
        for (OrderItemInfo o : orderItemInfoList) {
            Item item = new Item();
            item.setChineseName(o.getCnName());
            item.setEnglishName(o.getEnName());
            item.setAmount(o.getCount());
            item.setSku(o.getBarcode());
            item.setPrice(o.getCustomsUnitPrice());
            item.setCurrencyCode(currencyCode);
            itemList.add(item);
        }
        return itemList;
    }

    private void buildReceiver(Address receiver, CneOrder cneOrder) {
        cneOrder.setReceiver(receiver.getName());
        cneOrder.setPostCode(receiver.getPostCode());
        cneOrder.setPhone(StringUtils.hasText(receiver.getPhone()) ? receiver.getPhone() : receiver.getMobile());
        cneOrder.setEmail(receiver.getEmail());
        cneOrder.setCountry(receiver.getCountry());
        cneOrder.setProvince(receiver.getProvince());
        cneOrder.setCity(receiver.getCity());
        cneOrder.setAddr(receiver.getStreet());
    }

    private String getLabelFile(String logisticsNo, Map<String, String> configMap, String tid) {
        String labelUrl = "";
        try {
            byte[] pdfByte = downloadPdf(logisticsNo, configMap, tid);
            String labelFileName = String.format(LABEL_NAME, labelFolder, logisticsNo);
            labelUrl = getPdfLabel(pdfByte, labelFileName);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return labelUrl;
    }

    private GenerateOrderResponse.SuccessEntity processSuccessReply(OrderNewInfo orderInfo, CneResponse response, Map<String, String> configMap) throws IOException {
        List<CneResponse.Cne> errList = response.getErrList();
        String logisticsNo = errList.get(0).getcNo();
        String labelUrl = getLabelFile(logisticsNo, configMap, orderInfo.getTid());
        return buildSuccessEntity(orderInfo, logisticsNo, labelUrl, null, null);
    }

    private byte[] downloadPdf(String logisticsNo, Map<String, String> configMap, String tid) {
        TmsRequestLogEntity logEntity = requestLogService.recordLabelUrlLog(logisticsNo, "CNE", "CNE全球特惠", tid, "CNE");
        String ptemp = "label10x10_0";
        String labelUrl = String.format(cneLabelUrl, configMap.get(CONFIG_APP_KEY), logisticsNo, ptemp, buildAuth2MD5(configMap, logisticsNo));
        HttpHeaders headers = new HttpHeaders();
        List<MediaType> mediaTypeList = new ArrayList<>();
        mediaTypeList.add(MediaType.APPLICATION_PDF);
        headers.setAccept(mediaTypeList);
        HttpEntity<byte[]> entity = new HttpEntity<>(headers);
        LOGGER.info("final label url : {}", labelUrl);
        ResponseEntity<byte[]> response = restTemplate.exchange(labelUrl, HttpMethod.GET, entity, byte[].class);
        // 面单小于1K ,不正常
        if (response.getBody().length < 1024 || !FileUtils.isPdf(response.getBody())) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, labelUrl, JsonMapper.toJson(response.getBody()), logisticsNo);
            throw new RuntimeException(String.format("CNE 面单获取失败， logisticsNo:%s", logisticsNo));
        }
        requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, labelUrl, JsonMapper.toJson(response.getBody()), logisticsNo);
        return response.getBody();
    }

    private String buildAuth2MD5(Map<String, String> configMap, String cNos) {
        return MD5Util.crypt("" + configMap.get(CONFIG_APP_KEY) + cNos + configMap.get(CONFIG_SECRET));
    }

    public void doTrack(TmsPackageEntity tmsPackageEntity) {
        trackService.doTrack(tmsPackageEntity);
    }

    public void validateOrderRequest(OrderNewRequest request) {
        OrderNewInfo orderInfo = request.getOrderInfo();
        super.validateOrderRequest(request);
        Validator.isValid(orderInfo.getOrderItemInfoList(), attr -> !attr.isEmpty(), "OrderItemList不能为空");
        orderInfo.getOrderItemInfoList().forEach(item -> {
            Validator.isValid(item.getCnName(), StringUtils::hasText, "OrderItemInfo.cnName 不能为空");
            Validator.isValid(item.getCount(), Objects::nonNull, "OrderItemInfo.count 不能为空");
            Validator.isValid(item.getWeight(), Objects::nonNull, "OrderItemInfo.weight 不能为空");   //接口必填
            Validator.isValid(item.getUnitPrice(), Objects::nonNull, "OrderItemInfo.unitPrice 不能为空");
            Validator.isValid(item.getCustomsPrice(), Objects::nonNull, "OrderItemInfo.customsPrice 不能为空");
            Validator.isValid(item.getHsCode(), StringUtils::hasText, "OrderItemInfo.hsCode 不能为空");
        });
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.labelFolder += "CNE/";
        this.ossLabelFolder += "CNE/";
    }

    public void setLogisticsInfo(OrderNewRequest generateOrderRequest) {
        if (generateOrderRequest.getOrderInfo().getCustomsValueAmount() >= 15 && CountryCodeConstant.CANADA.equalsIgnoreCase(generateOrderRequest.getOrderInfo().getReceiveCountryCode())) {
            List<OrderItemInfo> orderItemInfoList = generateOrderRequest.getOrderInfo().getOrderItemInfoList();
            Double price = BigDecimal.valueOf(14.0 / orderItemInfoList.stream().mapToInt(OrderItemInfo::getCount).sum()).setScale(2, RoundingMode.DOWN).doubleValue();
            for (OrderItemInfo orderItemInfo : orderItemInfoList) {
                orderItemInfo.setCustomsPrice(price * orderItemInfo.getCount());
                orderItemInfo.setCustomsUnitPrice(price);
                orderItemInfo.setUnitPrice(price);
            }
            generateOrderRequest.getOrderInfo().setCustomsValueAmount(14.0);
        }
    }
}
