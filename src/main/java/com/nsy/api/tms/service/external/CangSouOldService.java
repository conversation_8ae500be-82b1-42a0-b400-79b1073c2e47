package com.nsy.api.tms.service.external;

import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.domain.Address;
import com.nsy.api.tms.domain.OrderInfo;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.logistics.cangsou.CangSouBaseRequest;
import com.nsy.api.tms.logistics.cangsou.CreateOrderRequest;
import com.nsy.api.tms.logistics.cangsou.CreateOrderResponse;
import com.nsy.api.tms.logistics.cangsou.Ec;
import com.nsy.api.tms.logistics.cangsou.Ec_Service;
import com.nsy.api.tms.logistics.cangsou.GetOrderByCodeRequest;
import com.nsy.api.tms.logistics.cangsou.GetOrderByCodeResponse;
import com.nsy.api.tms.logistics.cangsou.GetProductInventoryRequest;
import com.nsy.api.tms.logistics.cangsou.GetProductInventoryResponse;
import com.nsy.api.tms.repository.PackageRepository;
import com.nsy.api.tms.request.OrderRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.response.SecondaryNumberResponse;
import com.nsy.api.tms.response.cangsou.CangSouInventoryResponse;
import com.nsy.api.tms.service.TmsLogisticsChannelConfigService;
import com.nsy.api.tms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
//@TmsHandler(logisticsMethod = "仓搜")
public class CangSouOldService extends BaseLogisticsService implements InitializingBean {


    @Inject
    TmsLogisticsChannelConfigService logisticsChannelConfigService;

    @Autowired
    PackageRepository packageRepository;

    public static final String CONFIG_APP_KEY = "appKey";
    public static final String CONFIG_APP_TOKEN = "appToken";
    public static final String CREATE_ORDER_METHOD_NAME = "createOrder";
    public static final String GET_ORDER_BY_CODE = "getOrderByCode";
    public static final String GET_PRODUCT_INVENTORY = "getProductInventory";
    public static final String LABEL_NAME = "%sCANGSOU-%s";


    public static final String WAREHOUSE_CODE = "USCAJS";
    public static final String USPS_CA_0_5KG = "USPS_CA_0_5KG";
    public static final String FEDEX_GROUND_CA = "FEDEX_GROUND_CA";
    private static final Logger LOGGER = LoggerFactory.getLogger(SuDiGuanJiaService.class);


    @Override
    public void afterPropertiesSet() throws Exception {
        this.labelFolder += "CANGSOU/";
        this.ossLabelFolder += "CANGSOU/";
    }

    @Override
    public GenerateOrderResponse syncGenerateOrder(OrderRequest request, TmsLogisticsChannelConfigEntity tmsLogisticsChannelConfigEntity) {
        TmsRequestLogEntity logEntity = tmsRequestLogService.recordBaseInfoLog(request.getOrderInfo());
        GenerateOrderResponse response = new GenerateOrderResponse();
        Map<String, String> configMap = buildConfig(tmsLogisticsChannelConfigEntity);
        CreateOrderRequest createOrderRequest = buildLogisticsOrderRequest(request, configMap);
        try {
            CreateOrderResponse suDiOrderResponse = doRequest(createOrderRequest, CREATE_ORDER_METHOD_NAME, configMap, CreateOrderResponse.class);
            LOGGER.info("请求订单响应:{}", JsonMapper.toJson(suDiOrderResponse));
            if (isResponseOk(suDiOrderResponse)) {
                GenerateOrderResponse.SuccessEntity successEntity = successCallback(request.getOrderInfo(), createOrderRequest, suDiOrderResponse, logEntity);
                response.setSuccessEntity(successEntity);
            } else {
                GenerateOrderResponse.Error error = failCallback(createOrderRequest, suDiOrderResponse, logEntity);
                response.setError(error);
            }
        } catch (Exception e) {
            tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, JsonMapper.toJson(createOrderRequest), e.getMessage(), null);
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return response;
    }

    private GenerateOrderResponse.Error failCallback(CreateOrderRequest createOrderRequest, CreateOrderResponse suDiOrderResponse, TmsRequestLogEntity logEntity) {
        tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, JsonMapper.toJson(createOrderRequest), JsonMapper.toJson(suDiOrderResponse), null);
        return buildError(suDiOrderResponse.getAsk(), suDiOrderResponse.getMessage());
    }

    private GenerateOrderResponse.SuccessEntity successCallback(@Valid OrderInfo orderInfo, CreateOrderRequest createOrderRequest, CreateOrderResponse createOrderResponse, TmsRequestLogEntity logEntity) {
        String trackingNumber = createOrderResponse.getOrderCode();
        tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, JsonMapper.toJson(createOrderRequest), JsonMapper.toJson(createOrderResponse), trackingNumber);
        return buildSuccessEntity(orderInfo, trackingNumber, null, LogisticsMethodEnum.CANGSOU, null, null);
    }

    private boolean isResponseOk(CreateOrderResponse createOrderResponse) {
        return "Success".equalsIgnoreCase(createOrderResponse.getAsk());
    }

    @Override
    protected CreateOrderRequest buildLogisticsOrderRequest(OrderRequest orderRequest, Map<String, String> configMap) {
        OrderInfo orderInfo = orderRequest.getOrderInfo();
        CreateOrderRequest request = new CreateOrderRequest();
        request.setPlatform("OTHER");
        request.setAllocatedAuto("0");
        request.setWarehouseCode(WAREHOUSE_CODE);
        request.setShippingMethod(orderInfo.getLogisticsChannelCode());
        request.setReferenceNo(orderInfo.getBusinessKey());
        // 正式环境直接审核至 待发货状态
        if ("production".equalsIgnoreCase(env.getActiveProfiles()[0])) {
            request.setVerify(1);
        }
        buildAddress(request, orderInfo);
        buildOrderItem(request, orderInfo.getOrderItemInfoList());
        return request;
    }

    private void buildOrderItem(CreateOrderRequest request, List<OrderItemInfo> orderItemInfoList) {
        List<CreateOrderRequest.OrderItem> orderItemList = new ArrayList<>();
        for (OrderItemInfo orderItemInfo : orderItemInfoList) {
            CreateOrderRequest.OrderItem orderItem = new CreateOrderRequest.OrderItem();
            // TODO 临时需求 如果barcode 是 909789044800， 修改为X002IK3THR
            orderItem.setProductSku("909789044800".equals(orderItemInfo.getBarcode()) ? "X002IK3THR" : orderItemInfo.getBarcode());
            orderItem.setProductDeclaredValue(orderItemInfo.getCustomsPrice());
            orderItem.setProductNameEn(orderItemInfo.getEnName());
            orderItem.setQuantity(orderItemInfo.getCount());
            orderItemList.add(orderItem);
        }
        request.setOrderItemList(orderItemList);
    }

    private void buildAddress(CreateOrderRequest orderRequest, OrderInfo orderInfo) {
        Address receiveAddress = orderInfo.getReceiver();
        orderRequest.setCountryCode(orderInfo.getReceiveCountryCode());
        orderRequest.setProvince(receiveAddress.getProvince());
        orderRequest.setCity(receiveAddress.getCity());
        orderRequest.setZipCode(receiveAddress.getPostCode());
        orderRequest.setName(receiveAddress.getName());
        orderRequest.setCompany(receiveAddress.getCompany());
        orderRequest.setPhone(StringUtils.hasText(receiveAddress.getPhone()) ? receiveAddress.getPhone() : receiveAddress.getMobile());
        orderRequest.setCellPhone(StringUtils.hasText(receiveAddress.getMobile()) ? receiveAddress.getMobile() : receiveAddress.getPhone());
        orderRequest.setEmail(receiveAddress.getEmail());
        String street = receiveAddress.getStreet();
        if (street.length() > 30) {
            orderRequest.setAddress1(street.substring(0, 30));
            if (street.length() > 60) {
                orderRequest.setAddress2(street.substring(30, 60));
                orderRequest.setAddress3(street.substring(60));
            } else {
                orderRequest.setAddress2(street.substring(30));
            }
        } else {
            orderRequest.setAddress1(street);
        }

    }

    protected <T> T doRequest(CangSouBaseRequest request, String serviceMethod, Map<String, String> configMap, Class<T> responseClass) {
        Ec_Service service = new Ec_Service();
        Ec ec = service.getEcSOAP();
        String paramsJson = JsonMapper.toJson(request);
        String serviceName = serviceMethod;
        String appToken = configMap.get(CONFIG_APP_TOKEN);
        String appKey = configMap.get(CONFIG_APP_KEY);
        String response = ec.callService(paramsJson, appToken, appKey, serviceName);
        return JsonMapper.fromJson(response, responseClass);
    }

    public CangSouInventoryResponse getProductInventory(String barcode) {
        GetProductInventoryRequest request = new GetProductInventoryRequest();
        request.setPage(1);
        request.setPageSize(1);
        request.setProductSku(barcode);
        request.setWarehouseCode(WAREHOUSE_CODE);
        GetProductInventoryResponse getProductInventoryResponse = doRequest(request, GET_PRODUCT_INVENTORY, buildConfig(), GetProductInventoryResponse.class);
        CangSouInventoryResponse response = new CangSouInventoryResponse();
        if ("Success".equalsIgnoreCase(getProductInventoryResponse.getAsk()) && !getProductInventoryResponse.getDataList().isEmpty()) {
            response = buildCangSouInventoryResponse(getProductInventoryResponse);
            response.setAsk(getProductInventoryResponse.getAsk());
        } else {
            response.setAsk(getProductInventoryResponse.getAsk());
        }
        return response;
    }

    private CangSouInventoryResponse buildCangSouInventoryResponse(GetProductInventoryResponse getProductInventoryResponse) {
        CangSouInventoryResponse cangsouInventoryResponse = new CangSouInventoryResponse();
        GetProductInventoryResponse.Data data = getProductInventoryResponse.getDataList().get(0);
        cangsouInventoryResponse.setBarcode(data.getBarcode());
        cangsouInventoryResponse.setOnwayQty(data.getOnway());
        cangsouInventoryResponse.setSellableQty(data.getSellable());
        return cangsouInventoryResponse;
    }

    @Override
    public SecondaryNumberResponse getSecondaryNumber(TmsPackageEntity packageEntity) {
        if (StringUtils.hasText(packageEntity.getSecondaryNumber())) {
            return super.getSecondaryNumber(packageEntity);
        }
        String secondaryNumber = getSecondaryNumByApi(packageEntity);
        packageEntity.setSecondaryNumber(secondaryNumber);
        packageService.save(packageEntity);
        return super.getSecondaryNumber(packageEntity);


    }

    public String getSecondaryNumByApi(TmsPackageEntity packageEntity) {
        String secondaryNumber = "";
        TmsRequestLogEntity logEntity = tmsRequestLogService.retrieveSecondaryNumberLog(packageEntity);
        GetOrderByCodeRequest getOrderByCodeRequest = new GetOrderByCodeRequest();
        getOrderByCodeRequest.setOrderCode(packageEntity.getLogisticsNo());
        try {
            GetOrderByCodeResponse getOrderByCodeResponse = doRequest(getOrderByCodeRequest, GET_ORDER_BY_CODE, buildConfig(), GetOrderByCodeResponse.class);
            if ("Success".equalsIgnoreCase(getOrderByCodeResponse.getAsk())
                    && !getOrderByCodeResponse.getDataList().isEmpty() && StringUtils.hasText(getOrderByCodeResponse.getDataList().get(0).getTrackingNo())) {
                secondaryNumber = getOrderByCodeResponse.getDataList().get(0).getTrackingNo();
                tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, JsonMapper.toJson(getOrderByCodeRequest), JsonMapper.toJson(getOrderByCodeResponse), packageEntity.getLogisticsNo());
            } else {
                LOGGER.info("订单{}，暂无跟踪号", packageEntity.getTid());
                tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, JsonMapper.toJson(getOrderByCodeRequest), JsonMapper.toJson(getOrderByCodeResponse), packageEntity.getLogisticsNo());
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, JsonMapper.toJson(getOrderByCodeRequest), e.getMessage(), packageEntity.getLogisticsNo());
        }
        return secondaryNumber;
    }

    private Map<String, String> buildConfig() {
        // todo 临时写死
        Map<String, String> configMap = new HashMap<>();
        configMap.put(CONFIG_APP_KEY, "e9fb4c5d11302318fb0b62579668062d");
        configMap.put(CONFIG_APP_TOKEN, "e7c9862ad25860362705dc54d5a9cea7");
        return configMap;
    }
}
