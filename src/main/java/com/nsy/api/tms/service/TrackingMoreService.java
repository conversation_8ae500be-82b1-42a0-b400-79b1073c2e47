package com.nsy.api.tms.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.constants.TrackingConstant;
import com.nsy.api.tms.dao.entity.SystemConfigEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsCompanyEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.dao.entity.TmsRouteRecordEntity;
import com.nsy.api.tms.enumeration.LocationEnum;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.enumeration.TrackType;
import com.nsy.api.tms.external.erp.ErpApiNewService;
import com.nsy.api.tms.external.erp.response.StoreWebsiteInfo;
import com.nsy.api.tms.external.oms.publish.OmsPublishApiService;
import com.nsy.api.tms.external.oms.publish.response.ErpWebsiteInfo;
import com.nsy.api.tms.external.wms.WmsApiService;
import com.nsy.api.tms.external.wms.request.OverseasWarehouseOrderPackageTimeUpdateRequest;
import com.nsy.api.tms.external.wms.request.TmsNotifyPackageExceptionRequest;
import com.nsy.api.tms.logistics.trackingmore.TrackingMoreApiClient;
import com.nsy.api.tms.logistics.trackingmore.TrackingMoreApiPathConstant;
import com.nsy.api.tms.logistics.trackingmore.request.TrackingMoreCreateInfo;
import com.nsy.api.tms.logistics.trackingmore.response.TrackingMoreCreateOrderResponse;
import com.nsy.api.tms.logistics.trackingmore.response.TrackingMoreSearchOrderResponse;
import com.nsy.api.tms.repository.SystemConfigRepository;
import com.nsy.api.tms.repository.TmsLogisticsCompanyRepository;
import com.nsy.api.tms.utils.JsonMapper;
import com.nsy.api.tms.utils.Key;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.inject.Inject;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.nsy.api.tms.service.TmsRequestLogService.TRACKING_MORE_GET_API;

@Service
public class TrackingMoreService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TrackingMoreService.class);
    private static final Map<String, String> COURIER_CODE_MAP = new HashMap<>();

    static {
        COURIER_CODE_MAP.put("顺丰国际", "sfb2c");
        COURIER_CODE_MAP.put("云途", "yunexpress");
        COURIER_CODE_MAP.put("鑫宇隆", "4px");
    }

    @Autowired
    TrackingMoreApiClient apiClient;

    @Autowired
    TmsRequestLogService tmsRequestLogService;

    @Inject
    TmsRouteService tmsRouteService;

    @Inject
    PackageService packageService;
    @Autowired
    private SystemConfigRepository systemConfigRepository;
    @Inject
    RestTemplate restTemplate;
    @Resource
    ErpApiNewService erpApiNewService;
    @Resource
    WmsApiService wmsApiService;
    @Resource
    OmsPublishApiService omsPublishApiService;
    @Autowired
    TmsLogisticsCompanyRepository companyRepository;
    @Autowired
    TmsLogisticsChannelConfigService channelConfigService;

    public void createOrder(List<TmsPackageEntity> tmsPackageEntityList) {
        Map<String, TmsRequestLogEntity> tmsRequestLogEntityMap = new HashMap<>();
        SystemConfigEntity methodList = systemConfigRepository.findFirstByConfigName(TrackingConstant.NEED_CREATE_51TRACKING_METHOD);
        if (methodList == null || !StringUtils.hasText(methodList.getConfigValue())) {
            LOGGER.error("未配置需要加入51track的物流方式" + TrackingConstant.NEED_CREATE_51TRACKING_METHOD);
            return;
        }
        Map map = (Map) JSON.parse(methodList.getConfigValue());
        List<TrackingMoreCreateInfo> trackingMoreCreateInfoList = tmsPackageEntityList.stream()
            .filter(tmsPackageEntity -> map.containsKey(tmsPackageEntity.getLogisticsMethod()) && StrUtil.isNotBlank(tmsPackageEntity.getTmsTid()))
            .map(tmsPackageEntity -> {
                TmsRequestLogEntity tmsRequestLogEntity = tmsRequestLogService.recordTrackingMoreBaseInfoLog(tmsPackageEntity, TmsRequestLogService.TRACKING_MORE_CREATE_API);
                tmsRequestLogEntityMap.put(tmsPackageEntity.getLogisticsNo(), tmsRequestLogEntity);
                TrackingMoreCreateInfo trackingMoreCreateInfo = new TrackingMoreCreateInfo();
                trackingMoreCreateInfo.setTrackingNumber(tmsPackageEntity.getLogisticsNo());
                trackingMoreCreateInfo.setCourierCode(String.valueOf(map.get(tmsPackageEntity.getLogisticsMethod())));
                if (LogisticsMethodEnum.DHL.getLogisticsMethod().equalsIgnoreCase(tmsPackageEntity.getLogisticsMethod()) && tmsPackageEntity.getShipDate() != null) {
                    trackingMoreCreateInfo.setShippingDate(DateUtils.format(tmsPackageEntity.getShipDate(), "yyyy-MM-dd HH:mm"));
                    trackingMoreCreateInfo.setTrackingShippingDate(DateUtils.format(tmsPackageEntity.getShipDate(), "yyyyMMdd"));
                }
                tmsPackageEntity.setTrackType(TrackType.TRACKING_MORE.name());
                return trackingMoreCreateInfo;
            }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(trackingMoreCreateInfoList)) {
            return;
        }
        TrackingMoreCreateOrderResponse createOrderResponse = apiClient.build(TrackingMoreApiPathConstant.CREATE)
            .post(trackingMoreCreateInfoList, TrackingMoreCreateOrderResponse.class);
        updateTmsRequestLogEntity(tmsRequestLogEntityMap, trackingMoreCreateInfoList, createOrderResponse);
        packageService.updatePackageList(tmsPackageEntityList);
    }

    private void updateTmsRequestLogEntity(Map<String, TmsRequestLogEntity> tmsRequestLogEntityMap, List<TrackingMoreCreateInfo> trackingMoreCreateInfoList, TrackingMoreCreateOrderResponse createOrderResponse) {
        createOrderResponse.getSuccess().stream().forEach(successItem -> {
            TmsRequestLogEntity tmsRequestLogEntity = tmsRequestLogEntityMap.get(successItem.getTrackingNumber());
            tmsRequestLogService.updateLog(tmsRequestLogEntity, TmsRequestLogEntity.SUCCESS, JsonMapper.toJson(trackingMoreCreateInfoList), JsonMapper.toJson(createOrderResponse), successItem.getTrackingNumber());
        });
        createOrderResponse.getError().stream().forEach(errorItem -> {
            TmsRequestLogEntity tmsRequestLogEntity = tmsRequestLogEntityMap.get(errorItem.getTrackingNumber());
            tmsRequestLogService.updateLog(tmsRequestLogEntity, TmsRequestLogEntity.EXCEPTION, JsonMapper.toJson(trackingMoreCreateInfoList), JsonMapper.toJson(createOrderResponse), errorItem.getTrackingNumber());
        });
    }

    public void track(TmsPackageEntity tmsPackageEntity) {
        try {
            TmsRequestLogEntity tmsRequestLogEntity = tmsRequestLogService.recordTrackingMoreBaseInfoLog(tmsPackageEntity, TRACKING_MORE_GET_API);
            List searchOrderResponseList = apiClient.build(String.format("%s%s", TrackingMoreApiPathConstant.GET, tmsPackageEntity.getLogisticsNo())).get(List.class);
            if (!CollectionUtils.isEmpty(searchOrderResponseList)) {
                TrackingMoreSearchOrderResponse searchOrderResponse = JsonMapper.fromJson(JsonMapper.toJson(searchOrderResponseList.get(0)), TrackingMoreSearchOrderResponse.class);
                tmsRequestLogService.updateLog(tmsRequestLogEntity, TmsRequestLogEntity.SUCCESS, String.format("%s%s", TrackingMoreApiPathConstant.GET, tmsPackageEntity.getLogisticsNo()), JsonMapper.toJson(searchOrderResponseList), tmsPackageEntity.getLogisticsNo());
                analysisTrackRoute(tmsPackageEntity, searchOrderResponse);
            } else {
                tmsPackageEntity.setRouteSearchTimes(tmsPackageEntity.getRouteSearchTimes() + 1);
                String format = String.format("%s%s", TrackingMoreApiPathConstant.GET, tmsPackageEntity.getLogisticsNo());
                LOGGER.error("物流单号为：{}的包裹调用51Tracking失败，请确认是否导入单号", tmsPackageEntity.getLogisticsNo());
                tmsRequestLogService.updateLog(tmsRequestLogEntity, TmsRequestLogEntity.FAIL, format, "单号不存在或未导入，response返回空", tmsPackageEntity.getLogisticsNo());
                packageService.save(tmsPackageEntity);
            }
        } catch (Exception e) {
            tmsPackageEntity.setRouteSearchTimes(tmsPackageEntity.getRouteSearchTimes() + 1);
            packageService.save(tmsPackageEntity);
            LOGGER.error("物流单号为：{}的包裹调用51Tracking接口失败", tmsPackageEntity.getLogisticsNo(), e);
        }
    }

    private void analysisTrackRoute(TmsPackageEntity packageEntity, TrackingMoreSearchOrderResponse searchOrderResponse) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String deliveryStatus = searchOrderResponse.getDeliveryStatus();
        PackageStatusEnum packageStatus = mappingStatus(deliveryStatus);
        String oldStatus = packageEntity.getStatus();
        if (null == packageStatus) {
            LOGGER.info("logisticsNo:{}追踪状态异常", packageEntity.getLogisticsNo());
            packageEntity.setRouteSearchTimes(packageEntity.getRouteSearchTimes() + 1);
            packageService.save(packageEntity);
            return;
        }
        boolean updateReceiveTime = false;
        boolean updateArrivalTime = false;
        TrackingMoreSearchOrderResponse.OriginInfo originInfo = searchOrderResponse.getOriginInfo();
        packageEntity.setRouteSearchTimes(packageEntity.getRouteSearchTimes() + 1);
        if (packageStatus == PackageStatusEnum.DELIVERED && searchOrderResponse.getLastestCheckpointTime() != null && packageEntity.getArrivalTime() == null) {
            packageEntity.setArrivalTime(searchOrderResponse.getLastestCheckpointTime());
            updateArrivalTime = true;
        }
        packageEntity.setRouteLastUpdateTime(searchOrderResponse.getLastestCheckpointTime());
        packageEntity.setStatus(packageStatus.getDesc());
        List<TmsRouteRecordEntity> insertRouteList = new ArrayList<>();

        if (null != originInfo) {
            DateTime receivedDate = DateUtil.parse(originInfo.getReceivedDate());
            if (receivedDate != null && !DateUtil.isSameTime(receivedDate, packageEntity.getArrivalTime() == null ? new Date() : packageEntity.getArrivalTime())) {
                packageEntity.setReceiveTime(receivedDate);
                updateReceiveTime = true;
            }
            List<TrackingMoreSearchOrderResponse.TrackInfo> trackInfoList = originInfo.getTrackInfo();
            Collections.reverse(trackInfoList);
            List<TmsRouteRecordEntity> routeRecordEntityList = tmsRouteService.findByLogisticsNoAndPackageIdOrderByIdAsc(packageEntity.getLogisticsNo(), packageEntity.getId());
            Map<Key, TmsRouteRecordEntity> routeRecordEntityMap = routeRecordEntityList.stream()
                .collect(Collectors.toMap(k -> Key.of(sdf.format(k.getAcceptTime()), k.getAcceptAddress()), Function.identity(), (k1, k2) -> k2));
            trackInfoList
                .stream().filter(trackInfo -> !routeRecordEntityMap.containsKey(Key.of(trackInfo.getCheckpointDate(), trackInfo.getLocaiton())))
                .forEach(trackInfo -> {
                    TmsRouteRecordEntity tmsRouteRecordEntity = persistTmsRouteRecord(packageEntity.getLogisticsNo(), trackInfo, packageEntity.getId());
                    insertRouteList.add(tmsRouteRecordEntity);
                });
            resetPackageStatusByRoute(packageEntity, insertRouteList, routeRecordEntityList);
        }
        String location = getLocation(packageEntity);
        // 即将送达 - 通知平台
        notifyDeliveryPlatform(packageEntity, insertRouteList, location);
        // 包裹异常 通知订单审核人
        notifyOrderAudit(oldStatus, packageStatus, packageEntity, location);
        if (updateReceiveTime || updateArrivalTime) {
            // 调用WMS API更新包裹时间
            updateWmsPackageTime(packageEntity, updateReceiveTime, updateArrivalTime);
        }
        packageService.save(packageEntity);
    }

    private void resetPackageStatusByRoute(TmsPackageEntity packageEntity, List<TmsRouteRecordEntity> insertRouteList, List<TmsRouteRecordEntity> routeRecordEntityList) {
        // 原飞航到美国才会更新状态
        if (LogisticsMethodEnum.YuanFeiHang.getLogisticsMethod().equals(packageEntity.getLogisticsMethod())
                && PackageStatusEnum.INFO_RECEIVED.getDesc().equals(packageEntity.getStatus())
                && (insertRouteList.stream().anyMatch(trackItem -> StrUtil.containsAnyIgnoreCase(trackItem.getRemark(), "Picked Up", "pick up"))
                || routeRecordEntityList.stream().anyMatch(trackItem -> StrUtil.containsAnyIgnoreCase(trackItem.getRemark(), "Picked Up", "pick up")))) {
            packageEntity.setStatus(PackageStatusEnum.TRANSIT.getDesc());
        }
    }

    private String getLocation(TmsPackageEntity packageEntity) {
        String location = LocationEnum.QUANZHOU.name();
        if (StringUtils.hasText(packageEntity.getLogisticsCompany())) {
            TmsLogisticsCompanyEntity byLogisticsCompany = companyRepository.findByLogisticsCompany(packageEntity.getLogisticsCompany());
            if (byLogisticsCompany != null) {
                location = byLogisticsCompany.getLocation();
            }
        }
        return location;
    }

    private void notifyOrderAudit(String oldStatus, PackageStatusEnum newStatus, TmsPackageEntity packageEntity, String location) {
        try {
            if (newStatus == null || Objects.equals(oldStatus, newStatus.getDesc()) || StrUtil.isBlank(packageEntity.getTmsTid())) {
                // 状态一致 即状态没有变更 不需要通知审核人
                return;
            }
            // 投递失败、运输过久、可能异常 =>  通知订单审核人
            if (newStatus == PackageStatusEnum.EXCEPTION || newStatus == PackageStatusEnum.DELIVERED_FAILED || newStatus == PackageStatusEnum.EXPIRED) {
                // 调用wms 查找订单审核人，发送钉钉消息
                TmsNotifyPackageExceptionRequest request = new TmsNotifyPackageExceptionRequest();
                request.setTid(packageEntity.getTid());
                request.setStatus(newStatus.getDesc());
                request.setLogisticsNo(packageEntity.getLogisticsNo());
                request.setLocation(location);
                request.setOperator("包裹物流更新");
                wmsApiService.notifyPackageException(request);
                LOGGER.info("通知业务包裹异常, 物流单号：{}", packageEntity.getLogisticsNo());
            }
        } catch (Exception e) {
            LOGGER.error(StrUtil.format("通知业务异常包裹时报错，物物流单号：{}", packageEntity.getLogisticsNo()), e);
        }

    }

    /**
     * 根据包裹状态 通知对应平台
     * <AUTHOR>
     * 2023-04-04
     */
    private void notifyDeliveryPlatform(TmsPackageEntity packageEntity, List<TmsRouteRecordEntity> insertRouteList, String location) {
        try {
            // 目前只支持云途 回传 官网
            if (!LogisticsMethodEnum.YUNTU.getLogisticsMethod().equals(packageEntity.getLogisticsMethod())) {
                return;
            }
            if (!location.equals(LocationEnum.QUANZHOU.name())) {
                return;
            }
            // out for delivery 即将到达通知
            if (insertRouteList.stream().anyMatch(route -> StringUtils.hasText(route.getRemark())
                    && (route.getRemark().equalsIgnoreCase("out for delivery") || route.getRemark().contains("out for delivery")))) {
                notifyWebsite(packageEntity, "out for delivery");
            }
            // delivered 到达
            if (insertRouteList.stream().anyMatch(route -> StringUtils.hasText(route.getRemark())
                    && (route.getRemark().equalsIgnoreCase("delivered") || route.getRemark().contains("delivered")))) {
                notifyWebsite(packageEntity, "delivered");
            }
        } catch (Exception e) {
            LOGGER.error(StrUtil.format("包裹即将到达/到达 通知官网报错，物流单号{}", packageEntity.getLogisticsNo()), e);
        }

    }

    private void notifyWebsite(TmsPackageEntity packageEntity, String type) {
        try {
            if (packageEntity.getStoreId() == null) {
                LOGGER.error("店铺id为空，无法找到对应站点");
                return;
            }
            StoreWebsiteInfo erpStoreWebsiteId = erpApiNewService.getErpStoreWebsiteId(packageEntity.getStoreId());
            if (erpStoreWebsiteId == null || erpStoreWebsiteId.getConfigWebsiteId() == null) {
                LOGGER.error("erp店铺站点为空，无法找到对应站点");
                return;
            }
            ErpWebsiteInfo websiteConfigInfo = omsPublishApiService.getWebsiteConfigInfo(erpStoreWebsiteId.getConfigWebsiteId());
            if (websiteConfigInfo == null || websiteConfigInfo.getWebsiteUrl() == null || websiteConfigInfo.getWebsiteToken() == null) {
                LOGGER.error("oms配置为空，无法调用链接");
                return;
            }
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON_UTF8));
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("token", websiteConfigInfo.getWebsiteToken());
            params.add("item_no", packageEntity.getTid());
            params.add("logistics_number", packageEntity.getLogisticsNo());
            params.add("type", type);
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(String.format("http://%s/api-shippping-delivery.html", websiteConfigInfo.getWebsiteUrl()), requestEntity, String.class);
            LOGGER.info("通知平台包裹送达/即将送达, 物流单号：{}， 返回{}", packageEntity.getLogisticsNo(), JsonMapper.toJson(responseEntity));
        } catch (Exception e) {
            LOGGER.error("包裹状态更新通知DearLover错误：", e);
        }
    }


    private TmsRouteRecordEntity persistTmsRouteRecord(String logisticsNo, TrackingMoreSearchOrderResponse.TrackInfo trackInfo, Integer packageId) {
        TmsRouteRecordEntity routeRecordEntity = new TmsRouteRecordEntity();
        routeRecordEntity.setPackageId(packageId);
        routeRecordEntity.setLogisticsNo(logisticsNo);
        routeRecordEntity.setStatus(trackInfo.getCheckpointDeliveryStatus());
        routeRecordEntity.setAcceptTime(DateUtil.parse(trackInfo.getCheckpointDate()));
        routeRecordEntity.setAcceptAddress(trackInfo.getLocaiton());
        routeRecordEntity.setRemark(trackInfo.getTrackingDetail());
        tmsRouteService.save(routeRecordEntity);
        return routeRecordEntity;
    }


    private PackageStatusEnum mappingStatus(String deliveryStatus) {
        PackageStatusEnum packageStatusEnum = null;
        switch (deliveryStatus) {
            case "transit":
                packageStatusEnum = PackageStatusEnum.TRANSIT;
                break;
            case "pickup":
                packageStatusEnum = PackageStatusEnum.PENDING_PICKUP;
                break;
            case "delivered":
                packageStatusEnum = PackageStatusEnum.DELIVERED;
                break;
            case "expired":
                packageStatusEnum = PackageStatusEnum.EXPIRED;
                break;
            case "undelivered":
                packageStatusEnum = PackageStatusEnum.DELIVERED_FAILED;
                break;
            case "exception":
                packageStatusEnum = PackageStatusEnum.EXCEPTION;
                break;
            case "InfoReceived":
                packageStatusEnum = PackageStatusEnum.INFO_RECEIVED;
                break;
            //todo not found -->trigger create order??
            case "notfound":
            case "pending":
                break;
            default:
                break;
        }
        return packageStatusEnum;
    }

    /**
     * 更新WMS包裹时间
     *
     * @param packageEntity 包裹实体
     * @param updateReceiveTime 是否更新接收时间
     * @param updateArrivalTime 是否更新到达时间
     */
    private void updateWmsPackageTime(TmsPackageEntity packageEntity, boolean updateReceiveTime, boolean updateArrivalTime) {
        try {
            OverseasWarehouseOrderPackageTimeUpdateRequest request = new OverseasWarehouseOrderPackageTimeUpdateRequest();
            request.setLogisticsNo(packageEntity.getLogisticsNo());

            if (updateReceiveTime && packageEntity.getReceiveTime() != null) {
                request.setPackagePickupTime(packageEntity.getReceiveTime());
            }

            if (updateArrivalTime && packageEntity.getArrivalTime() != null) {
                request.setPackageSignedTime(packageEntity.getArrivalTime());
            }

            // 调用WMS API更新包裹时间
            wmsApiService.updatePackageTime(request);
        } catch (Exception e) {
            LOGGER.error("更新WMS包裹时间失败, 物流单号：{}", packageEntity.getLogisticsNo(), e);
        }
    }
}
