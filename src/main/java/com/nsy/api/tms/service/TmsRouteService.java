package com.nsy.api.tms.service;

import com.nsy.api.tms.dao.entity.TmsRouteRecordEntity;
import com.nsy.api.tms.repository.TmsRouteRepository;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class TmsRouteService {
    @Inject
    TmsRouteRepository tmsRouteRepository;

    public List<TmsRouteRecordEntity> findByLogisticsNoOrderByCreateDateDesc(String logisticsNo) {
        return tmsRouteRepository.findByLogisticsNoOrderByCreateDateDesc(logisticsNo);
    }

    public void save(TmsRouteRecordEntity routeRecordEntity) {
        tmsRouteRepository.save(routeRecordEntity);
    }

    public List<TmsRouteRecordEntity> saveRouteRecordEntityList(List<TmsRouteRecordEntity> routeRecordEntityList) {
        return tmsRouteRepository.saveAll(routeRecordEntityList);
    }

    public List<TmsRouteRecordEntity> findByLogisticsNoOrderByIdAsc(String logisticsNo) {
        return tmsRouteRepository.findByLogisticsNoOrderByIdAsc(logisticsNo);
    }

    public List<TmsRouteRecordEntity> findByLogisticsNoAndPackageIdOrderByIdAsc(String logisticsNo, Integer packageId) {
        return tmsRouteRepository.findByLogisticsNoAndPackageIdOrderByIdAsc(logisticsNo, packageId);
    }
}
