package com.nsy.api.tms.service.external.refactor;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.exception.ResourceNotFoundException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.constants.CountryCodeConstant;
import com.nsy.api.tms.dao.entity.TmsAlertTaskEntity;
import com.nsy.api.tms.dao.entity.TmsAsyncRequestQueueEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsAccountConfigEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsAccountEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.domain.order.OrderNewInfo;
import com.nsy.api.tms.enumeration.LocationEnum;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.enumeration.TmsTaskTriggerConditionEnum;
import com.nsy.api.tms.logistics.cangsou.v2.TmsGetStockInOrderInfo;
import com.nsy.api.tms.logistics.goodcang.stockin.StockTransferTrackingResponse;
import com.nsy.api.tms.logistics.goodcang.stockin.WmsCreateStockInOrderRequest;
import com.nsy.api.tms.repository.TmsLogisticsAccountConfigRepository;
import com.nsy.api.tms.repository.TmsLogisticsAccountRepository;
import com.nsy.api.tms.repository.TmsLogisticsChannelConfigRepository;
import com.nsy.api.tms.request.BaseLogisticsOrderRequest;
import com.nsy.api.tms.request.OrderNewRequest;
import com.nsy.api.tms.request.OverseaStockRequest;
import com.nsy.api.tms.request.SkuDTO;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.response.PrintLabelResponse;
import com.nsy.api.tms.response.SecondaryNumberResponse;
import com.nsy.api.tms.service.AliyunOssService;
import com.nsy.api.tms.service.PackageNewService;
import com.nsy.api.tms.service.PackageService;
import com.nsy.api.tms.service.TmsAlertTaskService;
import com.nsy.api.tms.service.TmsAsyncRequestQueueService;
import com.nsy.api.tms.service.TmsConfigService;
import com.nsy.api.tms.service.TmsLogisticsAccountService;
import com.nsy.api.tms.service.TmsRequestLogService;
import com.nsy.api.tms.service.external.refactor.winit.model.OverseaPrintShipmentLabelRequest;
import com.nsy.api.tms.utils.FileUtils;
import com.nsy.api.tms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;

@Service
public abstract class BaseLogisticsNewService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseLogisticsNewService.class);

    @Autowired
    PackageService packageService;
    @Autowired
    RestTemplate restTemplate;
    @Autowired
    TmsRequestLogService requestLogService;
    @Autowired
    TmsLogisticsAccountRepository logisticsAccountRepository;
    @Autowired
    TmsLogisticsAccountService logisticsAccountService;
    @Autowired
    TmsLogisticsChannelConfigRepository channelConfigRepository;
    @Autowired
    TmsLogisticsAccountConfigRepository logisticsAccountConfigRepository;
    @Autowired
    TmsAsyncRequestQueueService asyncRequestQueueService;
    @Autowired
    Environment env;
    @Autowired
    TmsAlertTaskService alertTaskService;
    @Autowired
    AliyunOssService aliyunOssService;
    @Autowired
    PackageNewService packageNewService;
    @Autowired
    TmsConfigService configService;

    @Value("${oss.label.folder}")
    public String ossLabelFolder;

    @Value("${label.folder}")
    String labelFolder;

    // 预处理wms和erp过来的数据
    public void preDeal(OrderNewRequest request) {

    }

    // 自带单号和面单校验
    public GenerateOrderResponse logisticsNoPushCheck(OrderNewRequest request) {
        return null;
    }

    public GenerateOrderResponse syncGenerateOrder(OrderNewRequest orderRequest, TmsLogisticsAccountEntity logisticsAccountEntity) {
        Map<String, String> configMap = buildAccountConfig(logisticsAccountEntity, orderRequest.getOrderInfo());
        String requestContent = buildSerializedRequest(orderRequest, logisticsAccountEntity);
        return doRequest(orderRequest, configMap, requestContent);
    }

    /**
     * 异步请求
     */
    public GenerateOrderResponse asyncGenerateOrder(OrderNewRequest request, TmsLogisticsAccountEntity logisticsAccountEntity) {
        GenerateOrderResponse orderResponse = new GenerateOrderResponse();
        GenerateOrderResponse.Error error = new GenerateOrderResponse.Error();
        List<TmsAsyncRequestQueueEntity> queueEntityList = asyncRequestQueueService.getByBusinessKey(request.getOrderInfo().getBusinessKey());
        String configMapString = JsonMapper.toJson(buildAccountConfig(logisticsAccountEntity, request.getOrderInfo()));
        String requestString = buildSerializedRequest(request, logisticsAccountEntity);
        if (queueEntityList.isEmpty()) {
            asyncRequestQueueService.saveAsyncRequest(request, configMapString, requestString);
        } else if (StringUtils.hasText(queueEntityList.get(0).getLogisticsNo())) {
            return JsonMapper.fromJson(queueEntityList.get(0).getResponseContent(), GenerateOrderResponse.class);
        }
        error.setCode("-1");
        error.setMessage("异步处理仍未返回运单");
        orderResponse.setError(error);
        return orderResponse;
    }

    /**
     * 异步请求需要重载该方法
     */
    protected String buildSerializedRequest(OrderNewRequest request, TmsLogisticsAccountEntity logisticsAccountEntity) {
        return "";
    }

    public GenerateOrderResponse doRequest(OrderNewRequest orderRequest, Map<String, String> configMap, String requestContent) {
        return null;
    }

    protected Map<String, String> buildAccountConfig(TmsLogisticsAccountEntity logisticsAccountEntity, OrderNewInfo orderInfo) {
        if (orderInfo.getOverseaSpace()) {
            Map<String, String> configMap = getOverseaConfigMap(orderInfo);
            if (configMap != null) return configMap;
        }
        List<TmsLogisticsAccountConfigEntity> logisticsAccountConfigEntityList = logisticsAccountConfigRepository.findByLogisticsAccountId(logisticsAccountEntity.getId());
        Map<String, String> configMap = new HashMap<>();
        logisticsAccountConfigEntityList.forEach(configEntry -> configMap.put(configEntry.getKey(), configEntry.getValue()));
        return configMap;
    }

    private Map<String, String> getOverseaConfigMap(OrderNewInfo orderInfo) {
        if (StrUtil.isBlank(orderInfo.getSpaceName())) {
            LOGGER.error("海外仓获取配置异常：无仓库名称");
            return null;
        }
        Map<String, String> configMap = configService.getConfigMap(orderInfo.getSpaceName());
        if (CollectionUtils.isEmpty(configMap)) {
            LOGGER.error("海外仓获取配置异常：无仓库config");
            return null;
        }
        return configMap;
    }

//    private Map<String, String> getOverseaConfigMap(String spaceName) {
//        if (StrUtil.isBlank(spaceName) {
//            LOGGER.error("海外仓获取配置异常：无仓库名称");
//            return null;
//        }
//        Map<String, String> configMap = configService.getConfigMap(orderInfo.getSpaceName());
//        if (CollectionUtils.isEmpty(configMap)) {
//            LOGGER.error("海外仓获取配置异常：无仓库config");
//            return null;
//        }
//        return configMap;
//    }

    protected abstract BaseLogisticsOrderRequest buildLogisticsOrderRequest(OrderNewRequest request, Map<String, String> configMap);

    public void doTrack(TmsPackageEntity packageEntity) {
    }

    // tid加随机码，主要是支持局部发货的场景，不然有可能同个订单号请求会出问题
    public String buildTmsTid(OrderNewInfo orderInfo) {
        String suffixTid = generateRandom(6);
        return trimTid(orderInfo.getBusinessKey(), 16) + "-" + suffixTid;
    }

    public String trimTid(String tid) {
        if (tid.length() > 14) {
            return tid.substring(0, 14);
        }
        return tid;
    }

    public String trimTid(String tid, int maxLength) {
        if (tid.length() > maxLength) {
            return tid.substring(0, maxLength);
        }
        return tid;
    }

    public String generateRandom(int length) {
        String sources = "0123456789";
        Random rand = new Random();
        StringBuilder flag = new StringBuilder();
        for (int j = 0; j < length; j++) {
            flag.append(sources.charAt(rand.nextInt(9)) + "");
        }
        return flag.toString();
    }

    public PrintLabelResponse printLabel(TmsPackageEntity tmsPackageEntity) {
        PrintLabelResponse printLabelResponse = new PrintLabelResponse();
        printLabelResponse.setTid(tmsPackageEntity.getTid());
        printLabelResponse.setBusinessKey(tmsPackageEntity.getBusinessKey());
        printLabelResponse.setLabelUrl(tmsPackageEntity.getLabelUrl());
        printLabelResponse.setLogisticsNo(tmsPackageEntity.getLogisticsNo());
        printLabelResponse.setTmsTid(tmsPackageEntity.getTmsTid());
        printLabelResponse.setLogisticsTid(tmsPackageEntity.getLogisticsTid());
        printLabelResponse.setSecondaryNumber(tmsPackageEntity.getSecondaryNumber());
        return printLabelResponse;
    }

    public void syncDeliveryInfo(TmsPackageEntity tmsPackageEntity) {

    }

    public void cancelOrder(TmsPackageEntity tmsPackageEntity) {
        tmsPackageEntity.setStatus(PackageStatusEnum.CANCEL.getDesc());
        packageService.save(tmsPackageEntity);
    }

    public void validateOrderRequest(OrderNewRequest orderRequest) {
    }


    /**
     * 图片格式面单
     */
    protected String getImageLabel(byte[] labelByte, String labelFileName) throws IOException {
        StringBuilder imagePathStringBuilder = new StringBuilder();
        String labelUrl;
        List<File> labelFiles = FileUtils.pdf2MultiImage(labelByte, labelFileName);
        for (File file : labelFiles) {
            String itemLabelOssUrl;
            itemLabelOssUrl = aliyunOssService.uploadToOss(ossLabelFolder, file);
            imagePathStringBuilder.append(itemLabelOssUrl).append(',');
        }
        labelUrl = imagePathStringBuilder.toString().substring(0, imagePathStringBuilder.toString().length() - 1);
        return labelUrl;
    }


    /**
     * PDF 格式面单
     */
    protected String getPdfLabel(byte[] labelByte, String labelFileName) throws IOException {
        File pdfFile = FileUtils.toPdf(labelByte, labelFileName);
        return aliyunOssService.uploadToOss(ossLabelFolder, pdfFile);
    }

    public void generateAlertTask(TmsPackageEntity packageEntity) {
        List<TmsAlertTaskEntity> alertTaskEntityList = alertTaskService.getPreviousTask(packageEntity.getLogisticsNo());
        if (!alertTaskEntityList.isEmpty()) return;
        TmsAlertTaskEntity tmsAlertTaskEntity = new TmsAlertTaskEntity();
        BeanUtils.copyProperties(packageEntity, tmsAlertTaskEntity);
        tmsAlertTaskEntity.setPackageStatus(packageEntity.getStatus());
        tmsAlertTaskEntity.setTriggerCondition(TmsTaskTriggerConditionEnum.ABNORMAL.getName());
        tmsAlertTaskEntity.setCreateBy("BaseTrackJob");
        alertTaskService.save(tmsAlertTaskEntity);
    }

    protected GenerateOrderResponse.SuccessEntity buildSuccessEntity(OrderNewInfo orderInfo, String trackingNumber, String labelUrl, String logisticsTid, String secondaryNumber) {
        TmsPackageEntity packageEntity = Optional.ofNullable(packageService.getAllByLogisticsNo(trackingNumber)).orElseGet(() ->
                packageService.buildBaseTmsPackageEntityBuilder(trackingNumber, orderInfo)
                        .labelUrl(labelUrl)
                        .invoicePrice(Optional.ofNullable(orderInfo.getCustomsValueAmount()).map(amount -> BigDecimal.valueOf(amount).setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(null))
                        .logisticsTid(logisticsTid)
                        .secondaryNumber(secondaryNumber)
                        .build()
        );
        packageEntity.setLocation(orderInfo.getLocation());
        if (orderInfo.getOverseaSpace()) {
            packageEntity.setKeyGroup(orderInfo.getSpaceName());
        }
        packageService.save(packageEntity);
        return getSuccessEntity(trackingNumber, packageEntity);
    }

    public GenerateOrderResponse.SuccessEntity getSuccessEntity(String trackingNumber, TmsPackageEntity packageEntity) {
        GenerateOrderResponse.SuccessEntity successEntity = new GenerateOrderResponse.SuccessEntity();
        successEntity.setLabelUrl(packageEntity.getLabelUrl());
        successEntity.setLogisticsTid(packageEntity.getLogisticsTid());
        successEntity.setLogisticsNo(trackingNumber);
        successEntity.setId(packageEntity.getId());
        successEntity.setSecondaryNumber(packageEntity.getSecondaryNumber());
        return successEntity;
    }

    protected GenerateOrderResponse.Error buildError(String code, String message) {
        GenerateOrderResponse.Error error = new GenerateOrderResponse.Error();
        error.setCode(code);
        error.setMessage(message);
        return error;
    }

    public SecondaryNumberResponse getSecondaryNumber(TmsPackageEntity packageEntity) {
        SecondaryNumberResponse response = new SecondaryNumberResponse();
        response.setLogisticsMethod(packageEntity.getLogisticsMethod());
        response.setLogisticsChannelCode(packageEntity.getLogisticsChannelCode());
        response.setSecondaryNumber(packageEntity.getSecondaryNumber());
        response.setPackageStatus(packageEntity.getStatus());
        return response;
    }


    public GenerateOrderResponse buildOrderResponse(OrderNewInfo orderInfo) {
        TmsPackageEntity packageEntity = packageService.findByBusinessKey(orderInfo.getBusinessKey())
                .stream().findFirst().orElseThrow(() -> new ResourceNotFoundException("包裹不存在"));
        GenerateOrderResponse generateOrderResponse = new GenerateOrderResponse();
        GenerateOrderResponse.SuccessEntity successEntity = buildSuccessEntity(orderInfo,
                packageEntity.getLogisticsNo(),
                packageEntity.getLabelUrl(),
                packageEntity.getTid(),
                packageEntity.getSecondaryNumber());
        generateOrderResponse.setSuccessEntity(successEntity);
        return generateOrderResponse;
    }

    public GenerateOrderResponse buildAsyncOrderResponse(OrderNewInfo orderInfo) {
        GenerateOrderResponse orderResponse = new GenerateOrderResponse();
        GenerateOrderResponse.Error error = new GenerateOrderResponse.Error();
        TmsAsyncRequestQueueEntity asyncRequestQueueEntity = asyncRequestQueueService.getByBusinessKey(orderInfo.getBusinessKey())
                .stream()
                .findFirst()
                .orElseThrow(() -> new ResourceNotFoundException("不存在该tid关联的异步请求"));
        switch (asyncRequestQueueEntity.getStatus()) {
            case -1:
            case 1:
                orderResponse = JsonMapper.fromJson(asyncRequestQueueEntity.getResponseContent(), GenerateOrderResponse.class);
                break;
            case 0:
                error.setCode("-1");
                error.setMessage("异步处理仍未返回运单");
                orderResponse.setError(error);
                break;
            default:
                break;
        }
        return orderResponse;
    }

    // 保证第三方物流所需的tid唯一
    public String getReferenceNoForLogistics(OrderNewInfo orderInfo) {
        LambdaQueryWrapper<TmsPackageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsPackageEntity::getTid, orderInfo.getTid()).eq(TmsPackageEntity::getLogisticsCompany, orderInfo.getLogisticsCompany());
        int count = packageNewService.count(wrapper);
        if (count > 0) {
            int i = count + 1;
            return orderInfo.getTid() + "-" + i;
        } else {
            return orderInfo.getTid();
        }
    }

    public void reSetUserPriceByTotalNum(List<OrderItemInfo> orderItemInfoList, OrderNewRequest request) {
        if (StrUtil.equalsIgnoreCase(request.getOrderInfo().getLocation(), LocationEnum.MISI.name())) {
            reSetUserPriceMISI(orderItemInfoList, request);
            return;
        }
        // 如果是欧盟的，且不是亚马逊的，要用原价
        if (CountryCodeConstant.EUROPEAN_UNION_COUNTRY_CODE.contains(request.getOrderInfo().getReceiveCountryCode()) && !StrUtil.containsAnyIgnoreCase(request.getOrderInfo().getPlatform(), "亚马逊", "AMAZON")) {
            // 非欧盟国家改价格
            return;
        }
        // 美国申报价值特殊处理
        if (StrUtil.equalsIgnoreCase(request.getOrderInfo().getReceiveCountryCode(), "US")) {
            if (orderItemInfoList.isEmpty()) {
                return;
            }
            // 生成2.12-2.38之间的随机数
            double min = 2.12;
            double max = 2.38;
            double randomPrice = min + Math.random() * (max - min);
            // 保留2位小数
            BigDecimal price = BigDecimal.valueOf(randomPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
            for (OrderItemInfo orderItemInfo : orderItemInfoList) {
                orderItemInfo.setCustomsUnitPrice(price.doubleValue());
                orderItemInfo.setCustomsPrice(price.doubleValue() * orderItemInfo.getCount());
                orderItemInfo.setUnitPrice(price.doubleValue());
            }
            request.getOrderInfo().setCustomsValueAmount(price.doubleValue() * orderItemInfoList.stream().mapToInt(OrderItemInfo::getCount).sum());
            return;
        }
        BigDecimal total;
        if (orderItemInfoList.isEmpty()) {
            return;
        }
        int num = orderItemInfoList.stream().mapToInt(OrderItemInfo::getCount).sum();
        if (num <= 3) {
            total = BigDecimal.valueOf(6);
        } else if (num <= 6) {
            total = BigDecimal.valueOf(14);
        } else {
            total = BigDecimal.valueOf(16);
        }
        Double price = total.divide(BigDecimal.valueOf(num), 2, BigDecimal.ROUND_DOWN).doubleValue();
        for (OrderItemInfo orderItemInfo : orderItemInfoList) {
            orderItemInfo.setCustomsUnitPrice(price);
            orderItemInfo.setCustomsPrice(price * orderItemInfo.getCount());
            orderItemInfo.setUnitPrice(price);
        }
        request.getOrderInfo().setCustomsValueAmount(price * num);
    }

    public void reSetUserPriceMISI(List<OrderItemInfo> orderItemInfoList, OrderNewRequest request) {
        if (orderItemInfoList.isEmpty()) {
            return;
        }
        BigDecimal totalNum = BigDecimal.ZERO;
        for (OrderItemInfo orderItemInfo : orderItemInfoList) {
            orderItemInfo.setCustomsUnitPrice(2d);
            orderItemInfo.setCustomsPrice(2d * orderItemInfo.getCount());
            orderItemInfo.setUnitPrice(2d);
        }
        request.getOrderInfo().setCustomsValueAmount(totalNum.doubleValue() * 2d);
    }

    public List<SkuDTO> getOverseaSpaceProductStock(Map<String, String> map, OverseaStockRequest request) {
        return new ArrayList<>();
    }

    public String printShipmentLabel(Map<String, String> map, OverseaPrintShipmentLabelRequest request) {
        return "";
    }

    public String createStockInOrder(Map<String, String> map, WmsCreateStockInOrderRequest request) {
        throw new BusinessServiceException("未实现创建入库单方法！");
    }

    public StockTransferTrackingResponse getStockInOrderInfo(Map<String, String> map, TmsGetStockInOrderInfo request) {
        throw new BusinessServiceException("未实现查找入库单方法！");
    }
}
