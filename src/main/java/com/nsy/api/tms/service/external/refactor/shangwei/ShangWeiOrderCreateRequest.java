package com.nsy.api.tms.service.external.refactor.shangwei;

import com.nsy.api.tms.request.BaseLogisticsOrderRequest;

import java.util.ArrayList;
import java.util.List;

/**
 * HXD
 * 2024/10/16
 **/

public class ShangWeiOrderCreateRequest extends BaseLogisticsOrderRequest {
    private String addressFirstStreet;
    private String city;
    private String clientCode;
    private String country;
    private String customerNumber;
    private String email;
    private int isSignature;
    private String personName;
    private List<ShangweiSku> skuList = new ArrayList<>();
    private String state;
    private String telNumber;
    private String transport;
    private String zipCode;

    public String getAddressFirstStreet() {
        return addressFirstStreet;
    }

    public void setAddressFirstStreet(String addressFirstStreet) {
        this.addressFirstStreet = addressFirstStreet;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCustomerNumber() {
        return customerNumber;
    }

    public void setCustomerNumber(String customerNumber) {
        this.customerNumber = customerNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public int getIsSignature() {
        return isSignature;
    }

    public void setIsSignature(int isSignature) {
        this.isSignature = isSignature;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public List<ShangweiSku> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<ShangweiSku> skuList) {
        this.skuList = skuList;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getTelNumber() {
        return telNumber;
    }

    public void setTelNumber(String telNumber) {
        this.telNumber = telNumber;
    }

    public String getTransport() {
        return transport;
    }

    public void setTransport(String transport) {
        this.transport = transport;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }
}
