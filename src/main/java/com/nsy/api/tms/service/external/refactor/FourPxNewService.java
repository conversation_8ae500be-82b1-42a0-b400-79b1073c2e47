package com.nsy.api.tms.service.external.refactor;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.annotation.LogisticsServiceHandler;
import com.nsy.api.tms.constants.LogisticsChannelConstant;
import com.nsy.api.tms.dao.entity.TmsLogisticsAccountEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.domain.Address;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.domain.order.OrderNewInfo;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.logistics.fourpx.FourPxLogisticsCommonParams;
import com.nsy.api.tms.logistics.fourpx.FourPxLogisticsGetOrderResponse;
import com.nsy.api.tms.logistics.fourpx.FourPxLogisticsLabelResponse;
import com.nsy.api.tms.logistics.fourpx.FourPxLogisticsOrderRequest;
import com.nsy.api.tms.logistics.fourpx.FourPxLogisticsOrderResponse;
import com.nsy.api.tms.request.OrderNewRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.response.PrintLabelResponse;
import com.nsy.api.tms.response.SecondaryNumberResponse;
import com.nsy.api.tms.utils.JsonMapper;
import com.nsy.api.tms.utils.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URI;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Service
@LogisticsServiceHandler(logisticsMethod = LogisticsMethodEnum.FOURPX)
public class FourPxNewService extends BaseLogisticsNewService implements InitializingBean {
    private static final Logger LOGGER = LoggerFactory.getLogger(FourPxNewService.class);

    private static final String CONFIG_APP_KEY = "4px_appKey";
    private static final String CONFIG_APP_SECRET = "4px_appSecret";
    private static final List<String> NEED_RETURN_OVERSEA = Arrays.asList("U4", "GP", "QC", "A4");
    private static final List<String> NO_RETURN_OVERSEA_COUNTRY = Arrays.asList("SE", "CH", "NL", "PT");
    public static final String LABEL_NAME = "%s4PX-%s";

    private String createOrderApiName;
    private String createOrderApiVersion;
    private String getOrderApiName;
    private String getOrderApiVersion;
    private String getLabelApiName;
    private String getLabelApiVersion;

    @Autowired
    FourPxTrackNewService pxTrackService;

    @Value("${4px.domain}")
    String fourDomain;

    @Value("${4px.track.domain}")
    String fourTrackDomain;

    @Override
    public void afterPropertiesSet() throws Exception {
        this.labelFolder += "4PX/";
        this.ossLabelFolder += "4PX/";
        this.createOrderApiName = "ds.xms.order.create";
        this.createOrderApiVersion = "1.1.0";
        this.getOrderApiName = "ds.xms.order.get";
        this.getOrderApiVersion = "1.1.0";
        this.getLabelApiName = "ds.xms.label.get";
        this.getLabelApiVersion = "1.0.0";
    }

    @Override
    public void preDeal(OrderNewRequest request) {
        reSetUserPriceByTotalNum(request.getOrderInfo().getOrderItemInfoList(), request);
        checkRequestInfo(request);
        processReceiver(request);
    }

    public void checkRequestInfo(OrderNewRequest generateOrderRequest) {
        String logisticsCompany = generateOrderRequest.getOrderInfo().getLogisticsCompany();
        String receiveCountryCode = generateOrderRequest.getOrderInfo().getReceiveCountryCode();
        Optional<OrderItemInfo> optional = generateOrderRequest.getOrderInfo().getOrderItemInfoList().stream().filter(t -> "口罩".equals(t.getCategoryName()) || "额温枪".equals(t.getCategoryName())).findAny();
        if (LogisticsChannelConstant.FOUR_PX.equalsIgnoreCase(logisticsCompany) && "CA".equalsIgnoreCase(receiveCountryCode) && optional.isPresent()) {
            throw new BusinessServiceException("4px 加拿大不能走口罩和额温枪");
        }
        if ("UK".equalsIgnoreCase(receiveCountryCode)) {
            generateOrderRequest.getOrderInfo().setReceiveCountryCode("GB");
            generateOrderRequest.getOrderInfo().getReceiver().setCountry("GB");
        }
        if (LogisticsChannelConstant.XINJIAPO_XIAOBAO.equalsIgnoreCase(logisticsCompany)
                && Arrays.asList("AT", "AU", "BE", "GR", "IE", "LU", "NL", "PT").contains(receiveCountryCode)) {
            throw new BusinessServiceException("奥地利、澳大利亚、比利时、希腊、爱尔兰、卢森堡、荷兰、葡萄牙，这8个国的订单限制走新加坡小包，请联系志杰切换物流");
        }
    }

    private void processReceiver(OrderNewRequest request) {
        Address receiver = request.getOrderInfo().getReceiver();
        if (!org.springframework.util.StringUtils.hasText(receiver.getProvince())) {
            receiver.setProvince(receiver.getCity());
        }
        receiver.setCity(receiver.getCity().toUpperCase(Locale.ROOT));
        if (org.springframework.util.StringUtils.startsWithIgnoreCase(receiver.getCity(), "ST. ")) {
            receiver.setCity(receiver.getCity().replace("ST. ", "SAINT "));
        } else if (org.springframework.util.StringUtils.startsWithIgnoreCase(receiver.getCity(), "ST.")) {
            receiver.setCity(receiver.getCity().replace("ST.", "SAINT "));
        } else if (org.springframework.util.StringUtils.startsWithIgnoreCase(receiver.getCity(), "ST ")) {
            receiver.setCity(receiver.getCity().replace("ST ", "SAINT "));
        } else if (org.springframework.util.StringUtils.startsWithIgnoreCase(receiver.getCity(), "SER. ")) {
            receiver.setCity(receiver.getCity().replace("SER. ", "SERGEANT "));
        } else if (org.springframework.util.StringUtils.startsWithIgnoreCase(receiver.getCity(), "SER.")) {
            receiver.setCity(receiver.getCity().replace("SER.", "SERGEANT "));
        } else if (org.springframework.util.StringUtils.startsWithIgnoreCase(receiver.getCity(), "FT ")) {
            receiver.setCity(receiver.getCity().replace("FT ", "FORT "));
        }
        receiver.setStreet(replace(receiver.getStreet()));
        receiver.setName(replace(receiver.getName()).replace('.', ' ').replace('&', ' '));
        receiver.setCompany(replace(receiver.getCompany()));
    }

    @Override
    public GenerateOrderResponse doRequest(OrderNewRequest orderRequest, Map<String, String> configMap, String requestContent) {
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(orderRequest.getOrderInfo());
        GenerateOrderResponse response = new GenerateOrderResponse();
        LOGGER.debug("请求参数:{}", requestContent);
        //invoke api
        URI baseUrl = getSignedUrl(createOrderApiName, createOrderApiVersion, requestContent, this.fourDomain, configMap);
        try {
            FourPxLogisticsOrderRequest fourPxLogisticsOrderRequest = JsonMapper.fromJson(requestContent, FourPxLogisticsOrderRequest.class);
            String reply = restTemplate.postForObject(baseUrl, fourPxLogisticsOrderRequest, String.class);
            FourPxLogisticsOrderResponse pxLogisticsOrderResponse = JsonMapper.fromJson(reply, FourPxLogisticsOrderResponse.class);
            LOGGER.debug("请求订单响应:{}", JsonMapper.toJson(pxLogisticsOrderResponse));
            //get result
            if (isResponseOk(pxLogisticsOrderResponse)) {
                GenerateOrderResponse.SuccessEntity successEntity = processSuccessReply(orderRequest.getOrderInfo(), fourPxLogisticsOrderRequest, pxLogisticsOrderResponse, configMap, logEntity);
                response.setSuccessEntity(successEntity);
            } else {
                GenerateOrderResponse.Error error = processFailReply(fourPxLogisticsOrderRequest, pxLogisticsOrderResponse, logEntity);
                response.setError(error);
            }
        } catch (Exception e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, requestContent, e.getMessage(), null);
            throw new RuntimeException(e);
        }
        return response;
    }

    @Override
    protected String buildSerializedRequest(OrderNewRequest orderRequest, TmsLogisticsAccountEntity logisticsAccountEntity) {
        // build api request params
        Map<String, String> configMap = buildAccountConfig(logisticsAccountEntity, orderRequest.getOrderInfo());
        FourPxLogisticsOrderRequest fourPxLogisticsOrderRequest = buildLogisticsOrderRequest(orderRequest, configMap);
        return JsonMapper.toJson(fourPxLogisticsOrderRequest);
    }

    @Override
    protected FourPxLogisticsOrderRequest buildLogisticsOrderRequest(OrderNewRequest request, Map<String, String> configMap) {
        OrderNewInfo orderInfo = request.getOrderInfo();
        FourPxLogisticsOrderRequest pxRequest = new FourPxLogisticsOrderRequest();
        pxRequest.setRefNo(orderInfo.getTmsTid());
//        buildIoss(request);
        pxRequest.setBusinessType("BDS");
        if (StringUtils.hasText(orderInfo.getIossNumber())) {
            pxRequest.setIossNo(orderInfo.getIossNumber());
        }
        pxRequest.setDutyType("U");
        pxRequest.setSalesPlatform(orderInfo.getPlatform());
        pxRequest.setIsInsure("N");
        //设置单个对象属性
        setObjectAttribute(pxRequest, orderInfo);
        //设置list对象属性
        setListAttribute(pxRequest, orderInfo);
        return pxRequest;
    }

//    private void buildIoss(OrderNewRequest request) {
//        if (!CountryCodeConstant.EUROPEAN_UNION_COUNTRY_CODE.contains(request.getOrderInfo().getReceiveCountryCode()) || StringUtils.hasText(request.getOrderInfo().getIossNumber())) {
//            return;
//        }
//        String location = request.getOrderInfo().getLocation();
//        if (!StringUtils.hasText(location) || "QUANZHOU".equalsIgnoreCase(location)) {
//            request.getOrderInfo().setIossNumber("IM4420001201");
//        }
//    }

    @Override
    public void doTrack(TmsPackageEntity packageEntity) {
        pxTrackService.doTrack(packageEntity);
    }

    @Override
    public PrintLabelResponse printLabel(TmsPackageEntity packageEntity) {
        /**
         * 返回面单前，获取物流渠道号码
         * */
        Map<String, String> configMap = logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());
        String fourPxChannelCode = packageEntity.getSecondaryNumber();
        if (!StringUtils.hasText(fourPxChannelCode)) {
            fourPxChannelCode = retrieveLogisticsChannelCode(packageEntity.getLogisticsNo(), configMap);
            packageEntity.setLogisticsTid(fourPxChannelCode);
            packageEntity.setSecondaryNumber(fourPxChannelCode);
            packageService.save(packageEntity);
        }
        if (StringUtils.hasText(packageEntity.getLabelUrl())) {
            return super.printLabel(packageEntity);
        }
        PrintLabelResponse.Error error = new PrintLabelResponse.Error();
        String labelUrl = getLabelFile(packageEntity.getLogisticsNo(), configMap, error);
        packageEntity.setLabelUrl(labelUrl);
        packageService.save(packageEntity);
        LOGGER.info("4PX实时获取面单：{}", JSONUtils.toJSON(packageEntity));
        if (StringUtils.hasText(packageEntity.getLabelUrl())) {
            return super.printLabel(packageEntity);
        } else {
            PrintLabelResponse printLabelResponse = new PrintLabelResponse();
            if (StringUtils.hasText(error.getCode())) {
                printLabelResponse = super.printLabel(packageEntity);
                printLabelResponse.setError(error);
            }
            return printLabelResponse;
        }
    }

    @Override
    public SecondaryNumberResponse getSecondaryNumber(TmsPackageEntity packageEntity) {
        if (StringUtils.hasText(packageEntity.getSecondaryNumber())) {
            return super.getSecondaryNumber(packageEntity);
        }
        String secondaryNumber = getSecondaryNumByApi(packageEntity);
        packageEntity.setSecondaryNumber(secondaryNumber);
        packageService.save(packageEntity);
        return super.getSecondaryNumber(packageEntity);
    }

    /**
     * 加密参数得到url
     *
     * @param apiName    接口名
     * @param apiBody    接口参数
     * @param apiVersion 接口版本
     * @return 加密后的url
     */
    public URI getSignedUrl(String apiName, String apiVersion, String apiBody, String domain, Map<String, String> configMap) {
        FourPxLogisticsCommonParams baseRequest = new FourPxLogisticsCommonParams();
        baseRequest.setAppKey(configMap.get(CONFIG_APP_KEY));
        baseRequest.setFormat("json");
        baseRequest.setMethod(apiName);
        baseRequest.setTimeStamp(System.currentTimeMillis());
        baseRequest.setVersion(apiVersion);
        baseRequest.setBody(apiBody);
        baseRequest.initSign(configMap.get(CONFIG_APP_SECRET)); //根据以上参数加密得到sign
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(domain);
        builder.queryParam("method", baseRequest.getMethod());
        builder.queryParam("app_key", baseRequest.getAppKey());
        builder.queryParam("v", baseRequest.getVersion());
        builder.queryParam("timestamp", baseRequest.getTimeStamp());
        builder.queryParam("format", baseRequest.getFormat());
        builder.queryParam("sign", baseRequest.getSign());
        return builder.build().encode().toUri();
    }

    private void setListAttribute(FourPxLogisticsOrderRequest pxRequest, OrderNewInfo orderInfo) {
        pxRequest.setParcelList(new ArrayList<>());
        FourPxLogisticsOrderRequest.Parcel parcel = new FourPxLogisticsOrderRequest.Parcel();
        BigDecimal weightUnitKg = new BigDecimal(Double.toString(orderInfo.getWeight()));
        BigDecimal multiplier = new BigDecimal(Double.toString(1000));
        Double weightUnitG = weightUnitKg.multiply(multiplier).doubleValue();
        parcel.setWeight(weightUnitG);
        parcel.setParcelValue(orderInfo.getCustomsValueAmount());
        parcel.setIncludeBattery("N");
        parcel.setCurrency("USD");
        parcel.setProductList(new ArrayList<>());
        parcel.setDeclareProductInfoList(new ArrayList<>());
        List<OrderItemInfo> orderItemInfoList = orderInfo.getOrderItemInfoList();
        orderItemInfoList.forEach((orderItem) -> {
            FourPxLogisticsOrderRequest.Product product = new FourPxLogisticsOrderRequest.Product();
            setProduct(product, orderItem);
            parcel.getProductList().add(product);
            FourPxLogisticsOrderRequest.DeclareProductInfo declareProduct = new FourPxLogisticsOrderRequest.DeclareProductInfo();
            setDeclareProduct(declareProduct, orderItem);
            parcel.getDeclareProductInfoList().add(declareProduct);
        });
        pxRequest.getParcelList().add(parcel);
    }

    private void setProduct(FourPxLogisticsOrderRequest.Product product, OrderItemInfo orderItem) {
        product.setProductName(orderItem.getCnName());
        product.setSkuCode(orderItem.getEnName());
        product.setProductDescription(orderItem.getDescription());
        product.setProductUnitPrice(orderItem.getUnitPrice());
        product.setQty(orderItem.getCount());
        product.setCurrency("USD");
    }

    private void setDeclareProduct(FourPxLogisticsOrderRequest.DeclareProductInfo declareProduct, OrderItemInfo orderItem) {
        declareProduct.setDeclareProductNameEn(orderItem.getEnName());
        declareProduct.setDeclareProductCodeQty(orderItem.getCount());
        declareProduct.setHscodeExport(orderItem.getHsCode());
        declareProduct.setDeclareProductNameCn(orderItem.getCnName());
        declareProduct.setDeclareUnitPriceExport(orderItem.getCustomsPrice() / orderItem.getCount());
        declareProduct.setDeclareUnitPriceImport(orderItem.getCustomsPrice() / orderItem.getCount());
        declareProduct.setCurrencyExport("USD");
        declareProduct.setCurrencyImport("USD");
        declareProduct.setPackageRemarks(orderItem.getSku());
    }

    private void setObjectAttribute(FourPxLogisticsOrderRequest pxRequest, OrderNewInfo orderInfo) {
        setSenderAndReceiver(pxRequest, orderInfo);
        FourPxLogisticsOrderRequest.DeliverTypeInfo deliverTypeInfo = new FourPxLogisticsOrderRequest.DeliverTypeInfo();
        deliverTypeInfo.setDeliverType("1");
        pxRequest.setDeliverTypeInfo(deliverTypeInfo);
        FourPxLogisticsOrderRequest.LogisticsServiceInfo logisticsServiceInfo = new FourPxLogisticsOrderRequest.LogisticsServiceInfo();
        String logisticsProductCode = orderInfo.getLogisticsChannelCode();
        logisticsServiceInfo.setLogisticsProductCode(logisticsProductCode);
        pxRequest.setLogisticsServiceInfo(logisticsServiceInfo);
        setReturnInfo(pxRequest, orderInfo);
    }

    private void setReturnInfo(FourPxLogisticsOrderRequest pxRequest, OrderNewInfo orderInfo) {
        FourPxLogisticsOrderRequest.ReturnInfo returnInfo = new FourPxLogisticsOrderRequest.ReturnInfo();
        returnInfo.setIsReturnOnDomestic("U");
        if (NEED_RETURN_OVERSEA.contains(orderInfo.getLogisticsChannelCode())) {
            if (NO_RETURN_OVERSEA_COUNTRY.contains(orderInfo.getReceiveCountryCode())) {
                returnInfo.setIsReturnOnOversea("U");
            } else {
                returnInfo.setIsReturnOnOversea("Y");
            }
        } else {
            if ("US".equalsIgnoreCase(orderInfo.getReceiveCountryCode())) {
                returnInfo.setIsReturnOnOversea("Y");
            } else {
                returnInfo.setIsReturnOnOversea("U");
            }
        }

        FourPxLogisticsOrderRequest.DomesticReturnAddr domesticReturn = new FourPxLogisticsOrderRequest.DomesticReturnAddr();
        domesticReturn.setCity("泉州");
        domesticReturn.setCompany("福建新时颖电子商务有限公司");
        domesticReturn.setCountry("CN");
        domesticReturn.setFirstName("庄小姐");
        domesticReturn.setPhone("0595-22916328");
        domesticReturn.setPhone2("18005958912");
        domesticReturn.setPostCode("362000");
        domesticReturn.setState("福建");
        domesticReturn.setStreet("丰泽区东海滨城鸿利达大街EA-9");
        returnInfo.setDomesticReturnAddr(domesticReturn);
        FourPxLogisticsOrderRequest.OverseaReturnAddr overseaReturn = new FourPxLogisticsOrderRequest.OverseaReturnAddr();
        overseaReturn.setCity("泉州");
        overseaReturn.setFirstName("庄小姐");
        overseaReturn.setCompany("福建新时颖电子商务有限公司");
        overseaReturn.setCountry("CN");
        overseaReturn.setPhone("0595-22916328");
        overseaReturn.setPhone2("18005958912");
        overseaReturn.setPostCode("362000");
        overseaReturn.setState("福建");
        overseaReturn.setStreet("丰泽区东海滨城鸿利达大街EA-9");
        returnInfo.setOverseaReturnAddr(overseaReturn);
        pxRequest.setReturnInfo(returnInfo);
    }

    private void setSenderAndReceiver(FourPxLogisticsOrderRequest pxRequest, OrderNewInfo orderInfo) {
        FourPxLogisticsOrderRequest.Sender sender = new FourPxLogisticsOrderRequest.Sender();
        Address orderSender = orderInfo.getSender();
        if (orderSender != null) {
            sender.setCountry(StringUtils.hasText(orderSender.getCountry()) ? orderSender.getCountry() : "CN");
            sender.setCity(orderSender.getCity());
            sender.setCompany(orderSender.getCompany());
            sender.setFirstName(orderSender.getName());
            sender.setPhone(orderSender.getPhone());
            sender.setPhone2(orderSender.getMobile());
            sender.setPostCode(orderSender.getPostCode());
            sender.setState(orderSender.getProvince());
            sender.setStreet(orderSender.getStreet());
        }
        pxRequest.setSender(sender);
        FourPxLogisticsOrderRequest.RecipientInfo receiver = new FourPxLogisticsOrderRequest.RecipientInfo();
        Address orderReceiver = orderInfo.getReceiver();
        receiver.setCity(orderReceiver.getCity());
        receiver.setCompany(orderReceiver.getCompany());
        receiver.setCountry(orderReceiver.getCountry());
        receiver.setFirstName(orderReceiver.getName());
        receiver.setPhone(orderReceiver.getPhone());
        receiver.setPhone2(orderReceiver.getMobile());
        receiver.setPostCode(orderReceiver.getPostCode());
        receiver.setState(orderReceiver.getProvince());
        receiver.setStreet(orderReceiver.getStreet());
        pxRequest.setRecipientInfo(receiver);
    }

    private GenerateOrderResponse.Error processFailReply(FourPxLogisticsOrderRequest request, FourPxLogisticsOrderResponse response, TmsRequestLogEntity logEntity) {
        String code = response.getErrors().get(0).getErrorCode();
        String message = response.getErrors().get(0).getErrorMsg();
        requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, JsonMapper.toJson(request), JsonMapper.toJson(response), null);
        return buildError(code, message);
    }

    private GenerateOrderResponse.SuccessEntity processSuccessReply(OrderNewInfo orderInfo, FourPxLogisticsOrderRequest request, FourPxLogisticsOrderResponse response, Map<String, String> configMap, TmsRequestLogEntity logEntity) throws InterruptedException {
        String fourPxNumber = response.getData().getLabelBarcode();
        int retry = 0;
        String trackingNumber = "";
        if (StringUtils.hasText(response.getData().getLogisticsChannelNo())) {
            trackingNumber = response.getData().getLogisticsChannelNo();
        } else {
            // 加拿大带电不带电渠道， 没有服务商号
//            if ("QY".equalsIgnoreCase(orderInfo.getLogisticsChannelCode()) || "QZ".equalsIgnoreCase(orderInfo.getLogisticsChannelCode())) {
//                trackingNumber = fourPxNumber;
//            }
            String retryCountStr = orderInfo.getExpressType();
            int retryCount = StrUtil.isNotBlank(retryCountStr) && NumberUtil.isNumber(retryCountStr) ? NumberUtil.parseInt(retryCountStr) : 1;
            while (retry < retryCount && !StringUtils.hasText(trackingNumber)) {
                Thread.sleep(3000);
                trackingNumber = retrieveLogisticsChannelCode(fourPxNumber, configMap);
                retry++;
            }
        }
        PrintLabelResponse.Error labelError = new PrintLabelResponse.Error();
        if (!StringUtils.hasText(trackingNumber)) {
//            getLabelFile(fourPxNumber, configMap, labelError);
            LOGGER.error(String.format("4PX 获取不到实际运单号,tid:%s, 4PX追踪号：%s, 原因：%s", orderInfo.getTid(), fourPxNumber, labelError.getMessage()));
        }
        String labelUrl = getLabelFile(fourPxNumber, configMap, labelError);
        requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, JsonMapper.toJson(request), JsonMapper.toJson(response), fourPxNumber);
        //
        if (LogisticsChannelConstant.FOURPX_EC_MISI.equalsIgnoreCase(orderInfo.getLogisticsChannelName()) && StrUtil.isNotBlank(trackingNumber)) {
            return buildSuccessEntity(orderInfo, trackingNumber, labelUrl, trackingNumber, fourPxNumber);
        }
        return buildSuccessEntity(orderInfo, fourPxNumber, labelUrl, fourPxNumber, StringUtils.hasText(trackingNumber) ? trackingNumber : null);
    }


    private String getLabelFile(String trackingNumber, Map<String, String> configMap, PrintLabelResponse.Error error) {
        Map<String, Object> params = new HashMap<>();
        params.put("request_no", trackingNumber);
        params.put("is_print_pick_info", "Y");
        URI baseUrl = getSignedUrl(getLabelApiName, getLabelApiVersion, JsonMapper.toJson(params), this.fourDomain, configMap);
        String reply = restTemplate.postForObject(baseUrl, params, String.class);
        LOGGER.info("获取面单响应:{}", reply);
        FourPxLogisticsLabelResponse labelResponse = JsonMapper.fromJson(reply, FourPxLogisticsLabelResponse.class);
        if (labelResponse.getData() == null
                || null == labelResponse.getData().getLabelUrlInfo()
                || !StringUtils.hasText(labelResponse.getData().getLabelUrlInfo().getLogisticsLabel())
                || "0".equalsIgnoreCase(labelResponse.getResult())) {
            error.setCode(labelResponse.getErrors().get(0).getErrorCode());
            error.setMessage(labelResponse.getErrors().get(0).getErrorMsg());
            return null;
        }
        String labelUrl = labelResponse.getData().getLabelUrlInfo().getLogisticsLabel();
        LOGGER.info("面单url:{}", labelUrl);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Arrays.asList(MediaType.IMAGE_PNG));
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<byte[]> labelByte = restTemplate.exchange(labelUrl, HttpMethod.GET, httpEntity, byte[].class); //下载面单
        String labelFileName = String.format(LABEL_NAME, labelFolder, trackingNumber);
        try {
            labelUrl = getPdfLabel(labelByte.getBody(), labelFileName);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return labelUrl;
    }

    public String retrieveLogisticsChannelCode(String trackingNumber, Map<String, String> configMap) {
        Map<String, Object> params = new HashMap<>();
        params.put("request_no", trackingNumber);
        URI baseUrl = getSignedUrl(getOrderApiName, getOrderApiVersion, JsonMapper.toJson(params), this.fourDomain, configMap);
        String reply = restTemplate.postForObject(baseUrl, params, String.class);
        LOGGER.info("获取物流渠道号码响应:{}", reply);
        FourPxLogisticsGetOrderResponse replyObject = JsonMapper.fromJson(reply, FourPxLogisticsGetOrderResponse.class);
        if (null != replyObject.getData()
                && !replyObject.getData().isEmpty()
                && null != replyObject.getData().get(0).getDsConsignmentNo()
                && StringUtils.hasText(replyObject.getData().get(0).getDsConsignmentNo().getLogisticsChannelNo())) {
            return replyObject.getData().get(0).getDsConsignmentNo().getLogisticsChannelNo();
        }
        return null;
    }

    private boolean isResponseOk(FourPxLogisticsOrderResponse reply) {
        return reply.getResult().equals("1");
    }

    private String getSecondaryNumByApi(TmsPackageEntity packageEntity) {
        Map<String, String> configMap = logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());
        return retrieveLogisticsChannelCode(packageEntity.getLogisticsNo(), configMap);
    }

    public void validateOrderRequest(OrderNewRequest request) {
        OrderNewInfo orderInfo = request.getOrderInfo();
        super.validateOrderRequest(request);
        Validator.isValid(orderInfo.getCustomsValueAmount(), Objects::nonNull, "customsValueAmount不能为空");
        Validator.isValid(orderInfo.getWeight(), Objects::nonNull, "weight不能为空");
        if (orderInfo.getWeight() == 0) {
            Validator.isValid(false, attr -> attr, "weight不能为0");
        }
        Validator.isValid(orderInfo.getReceiver(), Objects::nonNull, "receiver 节点不能为空");
        Validator.isValid(orderInfo.getReceiver().getPostCode(), StringUtils::hasText, "receiver.postCode 节点不能为空");
        Validator.isValid(orderInfo.getReceiver().getStreet(), StringUtils::hasText, "receiver.street 节点不能为空");
        Validator.isValid(orderInfo.getReceiver().getCity(), StringUtils::hasText, "receiver.city 节点不能为空");
        Validator.isValid(orderInfo.getReceiver().getProvince(), StringUtils::hasText, "receiver.province 节点不能为空");
        if (!StringUtils.hasText(orderInfo.getReceiver().getPhone())) {
            orderInfo.getReceiver().setPhone(orderInfo.getReceiver().getMobile());
        }
        Validator.isValid(orderInfo.getReceiver().getPhone(), StringUtils::hasText, "receiver.phone和receiver.mobile 节点至少一个不能为空");
        Validator.isValid(orderInfo.getReceiver().getName(), StringUtils::hasText, "receiver.name 节点不能为空");
        orderInfo.getOrderItemInfoList().forEach(item -> {
            Validator.isValid(item.getCount(), Objects::nonNull, "OrderItemInfo.count 不能为空");
            Validator.isValid(item.getCustomsPrice(), Objects::nonNull, "OrderItemInfo.customsPrice 不能为空");
            Validator.isValid(item.getUnitPrice(), Objects::nonNull, "OrderItemInfo.unitPrice 不能为空");
            Validator.isValid(item.getCnName(), StringUtils::hasText, "OrderItemInfo.cnName 节点不能为空");
            Validator.isValid(item.getEnName(), StringUtils::hasText, "OrderItemInfo.enName 节点不能为空");
            Validator.isValid(item.getDescription(), StringUtils::hasText, "OrderItemInfo.desctiption 节点不能为空");
        });
    }


    private String replace(String str) {
        return str.replace('|', ' ')
                .replace("\\", "").replace('\'', ' ').replace('‘', ' ')
                .replace('í', 'i').replace('é', 'e').replace('ó', 'o')
                .replace('ú', 'u').replace('ñ', 'n').replace('á', 'a')
                .replace('ö', 'o').replace('-', ' ').replace('\"', ' ');
    }
}
