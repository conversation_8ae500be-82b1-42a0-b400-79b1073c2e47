package com.nsy.api.tms.service.external;

import com.alibaba.fastjson.JSON;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.annotation.TmsHandler;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.domain.OrderInfo;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.logistics.cpam.Address;
import com.nsy.api.tms.logistics.cpam.Cargo;
import com.nsy.api.tms.logistics.cpam.request.CpamRequst;
import com.nsy.api.tms.logistics.cpam.response.CpamResponse;
import com.nsy.api.tms.request.OrderRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.utils.HttpsClientRequestFactory;
import com.nsy.api.tms.utils.JsonMapper;
import com.nsy.api.tms.utils.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 邮政小包对接
 *
 * <AUTHOR>
 * @date 2020-02-02 11:25
 */

@Service
@TmsHandler(logisticsMethod = "邮政小包")
public class CpamService extends BaseLogisticsService implements InitializingBean {
    @Value("${cpam.server.url}")
    String cpamServerUrl;
    @Value("${cpam.label.url}")
    String cpamLabelUrl;

    private static final Logger LOGGER = LoggerFactory.getLogger(CpamService.class);
    public static final String LABEL_NAME = "%sCPAM-%s";
    public static final String CONFIG_SECRET_KEY = "cpam_secretKey";
    private static final String EC_COMPANY_ID = "cpam_ecCompany_id";
    private static final String MAIL_TYPE = "cpam_mail_type";
    private static final String WH_CODE = "cpam_wh_code";

    RestTemplate restTemplate = new RestTemplate(new HttpsClientRequestFactory());

    @Override
    public void afterPropertiesSet() {
        this.labelFolder += "CPAM/";
        this.ossLabelFolder += "CPAM/";
    }

    @Override
    public GenerateOrderResponse syncGenerateOrder(OrderRequest request, TmsLogisticsChannelConfigEntity tmsLogisticsChannelConfigEntity) {
        TmsRequestLogEntity logEntity = tmsRequestLogService.recordBaseInfoLog(request.getOrderInfo());
        GenerateOrderResponse response = new GenerateOrderResponse();
        Map<String, String> configMap = buildConfig(tmsLogisticsChannelConfigEntity);
        CpamRequst cpamRequst = buildLogisticsOrderRequest(request, configMap);
        String requestJson = JSON.toJSONString(cpamRequst);
        MultiValueMap<String, String> requestParams = buildRequestParams(requestJson, configMap);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(requestParams, headers);
        try {
            ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(cpamServerUrl, entity, String.class);
            LOGGER.info("requestInfo: {}, responseInfo;{}", requestJson, responseEntity.getBody());
            CpamResponse cpamResponse = JSON.parseObject(responseEntity.getBody(), CpamResponse.class);
            if ("true".equals(cpamResponse.getSuccess()) && StringUtils.hasText(cpamResponse.getWayBillNo())) {
                GenerateOrderResponse.SuccessEntity successEntity = processSuccessReply(request.getOrderInfo(), cpamResponse, configMap);
                response.setSuccessEntity(successEntity);
                tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, requestJson, JsonMapper.toJson(cpamResponse), successEntity.getLogisticsNo());
            } else {
                GenerateOrderResponse.Error error = buildError(cpamResponse.getReason(), cpamResponse.getMsg());
                response.setError(error);
                tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, requestJson, JsonMapper.toJson(cpamResponse), null);
            }
        } catch (Exception e) {
            tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, JsonMapper.toJson(request), e.getMessage(), null);
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return response;
    }

    @Override
    protected CpamRequst buildLogisticsOrderRequest(OrderRequest request, Map<String, String> configMap) {
        OrderInfo orderInfo = request.getOrderInfo();
        CpamRequst cpamRequst = new CpamRequst();
        String currentTime = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").format(new Date());
        cpamRequst.setCreatedTime(currentTime);
        cpamRequst.setSenderNo(configMap.get(EC_COMPANY_ID));
        cpamRequst.setMailType(configMap.get(MAIL_TYPE));
        cpamRequst.setWhCode(configMap.get(WH_CODE));
        cpamRequst.setLogisticsOrderNo(orderInfo.getBusinessKey());
        cpamRequst.setBizProductNo(orderInfo.getLogisticsChannelCode());
        cpamRequst.setWeight(orderInfo.getWeight() * 1000);
        double totalWeight = 0D;
        double totalValue = 0D;
        for (OrderItemInfo itemInfo : orderInfo.getOrderItemInfoList()) {
            totalWeight = totalWeight + (itemInfo.getWeight() * itemInfo.getCount() * 1000);
            totalValue = totalValue + itemInfo.getCustomsPrice();
        }
        cpamRequst.setContentsTotalWeight(totalWeight);
        cpamRequst.setContentsTotalValue(totalValue);
        cpamRequst.setDeclareSource("2");
        cpamRequst.setDeclareType("1");
        cpamRequst.setDeclareCurrCode("USD");
        cpamRequst.setForecastshut("0");
        cpamRequst.setMailSign("2");
        cpamRequst.setSender(buildSender(orderInfo));
        cpamRequst.setReceiver(buildReceiver(orderInfo));
        cpamRequst.setItems(buildCargo(orderInfo));
        return cpamRequst;
    }

    private MultiValueMap<String, String> buildRequestParams(String requestJson, Map<String, String> configMap) {
        MultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>();
        requestParams.add("logistics_interface", requestJson);
        try {
            requestParams.add("data_digest", buildDataDigest(requestJson + configMap.get(CONFIG_SECRET_KEY)));
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            LOGGER.error("邮政小包 data_digest属性构建失败:{}", e.getMessage(), e);
        }
        requestParams.add("msg_type", "B2C_TRADE");
        requestParams.add("ecCompanyId", configMap.get(EC_COMPANY_ID));
        requestParams.add("data_type", "JSON");
        return requestParams;
    }

    private Address buildSender(OrderInfo orderInfo) {
        // build sender
        com.nsy.api.tms.domain.Address orderInfoSender = orderInfo.getSender();
        Address sender = new Address();
        sender.setName(orderInfoSender.getName());
        sender.setCompany(orderInfoSender.getCompany());
        sender.setPostCode(orderInfoSender.getPostCode());
        sender.setNation(orderInfoSender.getCountry());
        //寄件人移动电话必须是13位数字(86加手机号码);
        if (StringUtils.hasText(orderInfoSender.getMobile())) {
            String mobile = orderInfoSender.getMobile();
            if (orderInfoSender.getMobile().startsWith("+86")) {
                mobile = orderInfoSender.getMobile().substring(1);
            } else if (!orderInfoSender.getMobile().startsWith("86")) {
                mobile = "86" + orderInfoSender.getMobile();
            }
            sender.setMobile(mobile);
        }
        sender.setPhone(StringUtils.hasText(orderInfoSender.getPhone()) ? orderInfoSender.getPhone() : orderInfoSender.getMobile());
        sender.setName(orderInfoSender.getCountry());
        sender.setProvince(orderInfoSender.getProvince());
        sender.setCity(orderInfoSender.getCity());
        sender.setCounty(orderInfoSender.getCounty());
        sender.setAddress(StringUtils.hasText(orderInfoSender.getStreet()) ? orderInfoSender.getStreet() : orderInfoSender.getCity() + orderInfoSender.getCounty());
        sender.setLinker(orderInfoSender.getName());
        return sender;
    }

    private Address buildReceiver(OrderInfo orderInfo) {
        // build receiver
        com.nsy.api.tms.domain.Address orderInfoReceiver = orderInfo.getReceiver();
        Address receiver = new Address();
        receiver.setName(orderInfoReceiver.getName());
        receiver.setPostCode(StringUtils.hasText(orderInfoReceiver.getPostCode()) ? orderInfoReceiver.getPostCode() : "");
        receiver.setCompany(orderInfoReceiver.getCompany());
        receiver.setNation(orderInfoReceiver.getCountry());
        receiver.setPhone(StringUtils.hasText(orderInfoReceiver.getPhone()) ? orderInfoReceiver.getPhone() : orderInfoReceiver.getMobile());
        receiver.setName(orderInfoReceiver.getCountry());
        receiver.setProvince(orderInfoReceiver.getProvince());
        receiver.setCity(orderInfoReceiver.getCity());
        receiver.setCounty(orderInfoReceiver.getCounty());
        receiver.setAddress(StringUtils.hasText(orderInfoReceiver.getStreet()) ? orderInfoReceiver.getStreet() : orderInfoReceiver.getCity() + orderInfoReceiver.getCounty());
        receiver.setLinker(orderInfoReceiver.getName());
        receiver.setEmail(orderInfoReceiver.getEmail());
        return receiver;
    }

    private List<Cargo> buildCargo(OrderInfo orderInfo) {
        // build sender
        List<OrderItemInfo> itemInfos = orderInfo.getOrderItemInfoList();
        List<Cargo> items = new ArrayList<>();
        itemInfos.forEach(item -> {
            Cargo cargo = new Cargo();
            cargo.setCargoNo(item.getEnName());
            cargo.setCargoName(item.getCnName());
            cargo.setCargoNameEn(item.getEnName());
            cargo.setCargoTypeName(item.getCnName());
            cargo.setCargoQuantity(item.getCount());
            cargo.setCargoValue(item.getUnitPrice());
            cargo.setCost(item.getCustomsPrice());
            cargo.setCargoCurrency("USD");
            cargo.setCarogoWeight(1000 * item.getWeight());
            cargo.setCargoDescription(item.getDescription());
            cargo.setUnit("个");
            items.add(cargo);
        });
        return items;
    }

    private String buildDataDigest(String input) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        md5.update(input.getBytes("UTF-8"));
        return javax.xml.bind.DatatypeConverter.printBase64Binary(md5.digest());
    }

    private GenerateOrderResponse.SuccessEntity processSuccessReply(OrderInfo orderInfo, CpamResponse cpamResponse, Map<String, String> configMap) {
        String logisticsNo = cpamResponse.getWayBillNo();
        String labelUrl = saveLabelToFileAndOss(logisticsNo, configMap);
        return buildSuccessEntity(orderInfo, logisticsNo, labelUrl, LogisticsMethodEnum.CPAM, null, null);
    }

    private String saveLabelToFileAndOss(String logisticsNo, Map<String, String> configMap) {
        String labelFileName = String.format(LABEL_NAME, labelFolder, logisticsNo);
        byte[] pdfByte = downloadPdf(logisticsNo, configMap);
        try {
            return getPdfLabel(pdfByte, labelFileName);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private byte[] downloadPdf(String logisticsNo, Map<String, String> configMap) {
        MultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>();
        requestParams.add("ecCompanyId", configMap.get(EC_COMPANY_ID));
        try {
            requestParams.add("dataDigest", buildDataDigest(logisticsNo + configMap.get(CONFIG_SECRET_KEY)));
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        requestParams.add("barCode", logisticsNo);
        requestParams.add("version", "1.0");
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content_type", "application/x-www-form-urlencoded; charset=UTF-8");
        List<MediaType> mediaTypeList = new ArrayList<>();
        mediaTypeList.add(MediaType.APPLICATION_PDF);
        headers.setAccept(mediaTypeList);
        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(requestParams, headers);
        ResponseEntity<byte[]> response = restTemplate.exchange(cpamLabelUrl, HttpMethod.POST, entity, byte[].class);
        return response.getBody();
    }

    @Override
    public void validateOrderRequest(OrderRequest request) {
        OrderInfo orderInfo = request.getOrderInfo();
        super.validateOrderRequest(request);
        Validator.isValid(orderInfo.getBusinessKey(), StringUtils::hasText, "businessKey不能为空");
        Validator.isValid(orderInfo.getWeight(), attr -> !Objects.isNull(attr), "weight不能为空");
        Validator.isValid(orderInfo.getReceiver(), attr -> StringUtils.hasText(attr.getPhone()) || StringUtils.hasText(attr.getMobile()), "receiver.phone和receiver.mobile 节点至少一个不能为空");

        Validator.isValid(orderInfo.getOrderItemInfoList(), attr -> !attr.isEmpty(), "OrderItemList不能为空");
        orderInfo.getOrderItemInfoList().forEach(item -> {
            Validator.isValid(item.getCnName(), StringUtils::hasText, "OrderItemInfo.cnName 不能为空");
            Validator.isValid(item.getCount(), attr -> !Objects.isNull(attr), "OrderItemInfo.count 不能为空");
            Validator.isValid(item.getWeight(), attr -> !Objects.isNull(attr), "OrderItemInfo.weight 不能为空");
            Validator.isValid(item.getUnitPrice(), attr -> !Objects.isNull(attr), "OrderItemInfo.unitPrice 不能为空");
            Validator.isValid(item.getCustomsPrice(), attr -> !Objects.isNull(attr), "OrderItemInfo.customsPrice 不能为空");
        });

    }
}
