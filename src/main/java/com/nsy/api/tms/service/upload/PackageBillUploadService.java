package com.nsy.api.tms.service.upload;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.dao.entity.TmsLogisticsCompanyEntity;
import com.nsy.api.tms.enumeration.QuartzUploadQueueTypeEnum;
import com.nsy.api.tms.request.upload.PackageBillImport;
import com.nsy.api.tms.request.upload.UploadRequest;
import com.nsy.api.tms.response.upload.UploadResponse;
import com.nsy.api.tms.service.freight.PackageBillService;
import com.nsy.api.tms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class PackageBillUploadService implements IProcessUploadDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PackageBillUploadService.class);

    @Resource
    private PackageBillService packageBillService;

    @Override
    public QuartzUploadQueueTypeEnum type() {
        return QuartzUploadQueueTypeEnum.TMS_PACKAGE_BILL;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse response = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr())) {
            return response;
        }
        List<PackageBillImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), PackageBillImport.class);
        if (CollectionUtils.isEmpty(importList)) {
            return response;
        }
        List<PackageBillImport> errorList = new ArrayList<>();
        Map<Integer, TmsLogisticsCompanyEntity> companyEntityMap = new HashMap<>();
        JSONObject params = JSONUtil.parseObj(request.getUploadParams());
        Integer mode = params.getInt("mode");
        if (mode == null) {
            throw new BusinessServiceException("请选择导入模式");
        }
        // mode = 1 是覆盖，=2 是增量，否则则报错
        if (mode != 1 && mode != 2) {
            throw new BusinessServiceException("导入模式错误");
        }
        // 记录日志
        LOGGER.info("{}开始导入账单数据，模式：{}", request.getCreateBy(), mode == 1 ? "覆盖" : "增量");
        importList.forEach(row -> {
            try {
                validImport(row, mode);
                packageBillService.importBill(row, request, companyEntityMap, mode);
            } catch (RuntimeException e) {
                row.setErrorMsg(e.getMessage());
                errorList.add(row);
            }
        });
        if (!CollectionUtils.isEmpty(errorList)) {
            response.setDataJsonStr(JsonMapper.toJson(errorList));
        }
        return response;
    }

    private void validImport(PackageBillImport row, Integer mode) {
        if (StrUtil.isBlank(row.getLogisticsNo())) {
            throw new BusinessServiceException("物流单号必填");
        }
        if (row.getBusinessDate() == null) {
            throw new BusinessServiceException("业务日期必填");
        }

        // 转换所有后缀为Str的字符串字段为BigDecimal
        convertStringFieldsToBigDecimal(row);

        if (mode == 1 && row.getRealWeight() == null) {
            throw new BusinessServiceException("实际重量必填");
        }

    }

    /**
     * 将所有后缀为Str的字符串字段转换为对应的BigDecimal字段
     * @param row 包裹账单导入对象
     */
    private void convertStringFieldsToBigDecimal(PackageBillImport row) {
        // 实际重量
        if (StrUtil.isNotBlank(row.getRealWeightStr())) {
            try {
                row.setRealWeight(new BigDecimal(row.getRealWeightStr().trim()));
            } catch (NumberFormatException e) {
                throw new BusinessServiceException("实际重量格式错误：" + row.getRealWeightStr(), e);
            }
        }

        // 实际基础运费
        if (StrUtil.isNotBlank(row.getRealBasicFreightStr())) {
            try {
                row.setRealBasicFreight(new BigDecimal(row.getRealBasicFreightStr().trim()));
            } catch (NumberFormatException e) {
                throw new BusinessServiceException("实际基础运费格式错误：" + row.getRealBasicFreightStr(), e);
            }
        }

        // 实际燃油费
        if (StrUtil.isNotBlank(row.getRealFuelPriceStr())) {
            try {
                row.setRealFuelPrice(new BigDecimal(row.getRealFuelPriceStr().trim()));
            } catch (NumberFormatException e) {
                throw new BusinessServiceException("实际燃油费格式错误：" + row.getRealFuelPriceStr(), e);
            }
        }

        // 实际附加费
        if (StrUtil.isNotBlank(row.getRealSurchargePriceStr())) {
            try {
                row.setRealSurchargePrice(new BigDecimal(row.getRealSurchargePriceStr().trim()));
            } catch (NumberFormatException e) {
                throw new BusinessServiceException("实际附加费格式错误：" + row.getRealSurchargePriceStr(), e);
            }
        }

        // 实际偏远费
        if (StrUtil.isNotBlank(row.getRealRemoteAreaFeeStr())) {
            try {
                row.setRealRemoteAreaFee(new BigDecimal(row.getRealRemoteAreaFeeStr().trim()));
            } catch (NumberFormatException e) {
                throw new BusinessServiceException("实际偏远费格式错误：" + row.getRealRemoteAreaFeeStr(), e);
            }
        }

        // 实际关税
        if (StrUtil.isNotBlank(row.getRealTaxStr())) {
            try {
                row.setRealTax(new BigDecimal(row.getRealTaxStr().trim()));
            } catch (NumberFormatException e) {
                throw new BusinessServiceException("实际关税格式错误：" + row.getRealTaxStr(), e);
            }
        }

        // 实际手续费
        if (StrUtil.isNotBlank(row.getRealServiceFeeStr())) {
            try {
                row.setRealServiceFee(new BigDecimal(row.getRealServiceFeeStr().trim()));
            } catch (NumberFormatException e) {
                throw new BusinessServiceException("实际手续费格式错误：" + row.getRealServiceFeeStr(), e);
            }
        }

        // 实际挂号费
        if (StrUtil.isNotBlank(row.getRealRegistrationPriceStr())) {
            try {
                row.setRealRegistrationPrice(new BigDecimal(row.getRealRegistrationPriceStr().trim()));
            } catch (NumberFormatException e) {
                throw new BusinessServiceException("实际挂号费格式错误：" + row.getRealRegistrationPriceStr(), e);
            }
        }

        // 实际其他费用
        if (StrUtil.isNotBlank(row.getRealOtherFreightStr())) {
            try {
                row.setRealOtherFreight(new BigDecimal(row.getRealOtherFreightStr().trim()));
            } catch (NumberFormatException e) {
                throw new BusinessServiceException("实际其他费用格式错误：" + row.getRealOtherFreightStr(), e);
            }
        }
    }

}
