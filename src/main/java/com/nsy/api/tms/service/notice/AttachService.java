package com.nsy.api.tms.service.notice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.tms.constants.CommonConstant;
import com.nsy.api.tms.dao.entity.notic.AttachEntity;
import com.nsy.api.tms.mapper.notice.AttachMapper;
import com.nsy.api.tms.request.notice.NoticeMessageRequest;
import com.nsy.api.tms.service.privilege.AccessControlService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 公告关联附加(Attach)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-05 15:40:53
 */
@Service
public class AttachService extends ServiceImpl<AttachMapper, AttachEntity> {

    @Autowired
    private AccessControlService accessControlService;

    public void saveBatch(NoticeMessageRequest noticeMessageRequest) {
        if (CollectionUtils.isNotEmpty(noticeMessageRequest.getAttachList())) {
            this.saveBatch(noticeMessageRequest.getAttachList().stream().map(attach -> {
                AttachEntity attachEntity = new AttachEntity();
                BeanUtilsEx.copyProperties(attach, attachEntity);
                attachEntity.setNoticeMessageId(noticeMessageRequest.getNoticeMessageId());
                attachEntity.setCreateBy(accessControlService.getUserName());
                attachEntity.setUpdateBy(accessControlService.getUserName());
                return attachEntity;
            }).collect(Collectors.toList()), 500);
        }
    }

    public List<AttachEntity> queryByNoticeMessageId(Integer noticeMessageId) {
        return this.list(new LambdaQueryWrapper<AttachEntity>().eq(AttachEntity::getNoticeMessageId, noticeMessageId)
                .eq(AttachEntity::getIsDeleted, CommonConstant.NOT_DELETED));
    }

    public Boolean removeByNoticeMessageIdList(List<Integer> noticeMessageIdList) {
        return this.update(new UpdateWrapper<AttachEntity>().lambda().set(AttachEntity::getIsDeleted, CommonConstant.DELETED)
                .in(AttachEntity::getNoticeMessageId, noticeMessageIdList));
    }

}

