package com.nsy.api.tms.service.external.refactor;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.MD5Util;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.annotation.LogisticsServiceHandler;
import com.nsy.api.tms.dao.entity.TmsLogisticsAccountEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.domain.Address;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.domain.order.OrderNewInfo;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.logistics.yanwen.request.GetLabelRequest;
import com.nsy.api.tms.logistics.yanwen.request.ParcelInfo;
import com.nsy.api.tms.logistics.yanwen.request.ProductList;
import com.nsy.api.tms.logistics.yanwen.request.ReceiverInfo;
import com.nsy.api.tms.logistics.yanwen.request.SenderInfo;
import com.nsy.api.tms.logistics.yanwen.request.YanWenRequest;
import com.nsy.api.tms.logistics.yanwen.response.GetLabelResponse;
import com.nsy.api.tms.logistics.yanwen.response.SuccessOrderResponse;
import com.nsy.api.tms.logistics.yanwen.response.YanWenCreateOrderResponse;
import com.nsy.api.tms.request.OrderNewRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.response.SecondaryNumberResponse;
import com.nsy.api.tms.utils.JsonMapper;
import com.nsy.api.tms.utils.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

/**
 * HXD
 * 2021/5/11
 **/
@Service
@LogisticsServiceHandler(logisticsMethod = LogisticsMethodEnum.YANWEN)
public class YanWenNewService extends BaseLogisticsNewService implements InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(YanWenNewService.class);

    private static final String LABEL_NAME = "%sYanWen-%s";

    public static final String USER_ID = "userId";
    public static final String API_TOKEN = "apitoken";
    public static final String CREATE_ORDER = "/api/order";
    public static final String CREATE_ORDER_METHOD = "express.order.create";
    public static final String GET_LABEL_METHOD = "express.order.label.get";


    @Value("${yanwen.server.url}")
    private String yanWenBaseUrl;
    @Inject
    private RestTemplate restTemplate;


    @Override
    public void preDeal(OrderNewRequest request) {
        reSetUserPriceByTotalNum(request.getOrderInfo().getOrderItemInfoList(), request);
        reSetProductName(request);
    }

    private void reSetProductName(OrderNewRequest request) {
        List<OrderItemInfo> orderItemInfoList = request.getOrderInfo().getOrderItemInfoList();
        orderItemInfoList.forEach(item -> {
            String suits = StrUtil.replaceIgnoreCase(item.getEnName(), "suits", "");
            String result = StrUtil.replaceIgnoreCase(suits, "suit", "");
            item.setEnName(StrUtil.isBlank(result) ? "Clothes" : result);
            String suitsCn = StrUtil.replaceIgnoreCase(item.getCnName(), "套装", "");
            item.setCnName(StrUtil.isBlank(suitsCn) ? "衣服" : suitsCn);
        });
    }

    @Override
    protected YanWenRequest buildLogisticsOrderRequest(OrderNewRequest orderRequest, Map<String, String> configMap) {
        YanWenRequest request = new YanWenRequest();
        OrderNewInfo orderInfo = orderRequest.getOrderInfo();
        request.setChannelId(orderInfo.getLogisticsChannelCode());
        request.setOrderSource("nsy ERP");
        request.setUserId(configMap.get(USER_ID));
        request.setOrderNumber(orderInfo.getBusinessKey());
        request.setRemark(orderInfo.getTid());
        buildReceiverInfo(request, orderInfo);
        buildSenderInfo(request, orderInfo);
        ParcelInfo parcelInfo = new ParcelInfo();
        parcelInfo.setHasBattery(0);
        parcelInfo.setCurrency("USD");
        BigDecimal totalPrice = BigDecimal.ZERO;
        int totalWeight = 0;
        int totalQty = 0;
        List<ProductList> productLists = new ArrayList<>();
        for (OrderItemInfo itemInfo : orderInfo.getOrderItemInfoList()) {
            ProductList product = new ProductList();
            product.setGoodsNameCh(itemInfo.getCnName());
            product.setGoodsNameEn(itemInfo.getEnName());
            product.setPrice(BigDecimal.valueOf(itemInfo.getCustomsUnitPrice()));
            product.setQuantity(itemInfo.getCount());
            product.setWeight(BigDecimal.valueOf(itemInfo.getWeight() * 1000).intValue());
            product.setHscode(itemInfo.getHsCode());
            productLists.add(product);
            totalPrice = totalPrice.add(product.getPrice());
            totalWeight = totalWeight + product.getWeight();
            totalQty = totalQty + product.getQuantity();
        }
        parcelInfo.setProductList(productLists);
        parcelInfo.setTotalPrice(totalPrice);
        parcelInfo.setTotalWeight(totalWeight);
        parcelInfo.setTotalQuantity(totalQty);
        parcelInfo.setHeight(orderInfo.getHeight().intValue());
        parcelInfo.setWidth(orderInfo.getWidth().intValue());
        parcelInfo.setLength(orderInfo.getLength().intValue());
        parcelInfo.setIoss(orderInfo.getIossNumber());
        request.setParcelInfo(parcelInfo);
        return request;
    }

    private void buildSenderInfo(YanWenRequest request, OrderNewInfo orderInfo) {
        Address shipper = orderInfo.getSender();
        SenderInfo senderInfo = new SenderInfo();
        senderInfo.setName(shipper.getName());
        senderInfo.setPhone(shipper.getPhone());
        senderInfo.setCompany(shipper.getCompany());
        senderInfo.setEmail(shipper.getEmail());
        senderInfo.setCountry(shipper.getCountry());
        senderInfo.setState(shipper.getProvince());
        senderInfo.setCity(shipper.getCity());
        senderInfo.setZipCode(shipper.getPostCode());
        senderInfo.setAddress(shipper.getStreet());
        senderInfo.setTaxNumber(shipper.getTaxNumber());
        request.setSenderInfo(senderInfo);
    }

    private void buildReceiverInfo(YanWenRequest request, OrderNewInfo orderInfo) {
        ReceiverInfo receiverInfo = new ReceiverInfo();
        Address receiver = orderInfo.getReceiver();
        receiverInfo.setName(receiver.getName());
        receiverInfo.setPhone(StringUtils.hasText(receiver.getPhone()) ? receiver.getPhone() : receiver.getMobile());
        receiverInfo.setEmail(receiver.getEmail());
        receiverInfo.setCompany(receiver.getCompany());
        receiverInfo.setCountry(orderInfo.getReceiveCountryCode());
        receiverInfo.setState(receiver.getProvince());
        receiverInfo.setCity(receiver.getCity());
        receiverInfo.setZipCode(receiver.getPostCode());
        receiverInfo.setAddress(receiver.getStreet());

        if (StrUtil.equalsIgnoreCase(orderInfo.getReceiveCountryCode(), "BR")) {
            // 巴西等国家要设置税号
            receiverInfo.setTaxNumber(receiver.getTaxNumber());
        }
        request.setReceiverInfo(receiverInfo);
    }



    @Override
    public SecondaryNumberResponse getSecondaryNumber(TmsPackageEntity packageEntity) {
        return super.getSecondaryNumber(packageEntity);
    }

    @Override
    public GenerateOrderResponse doRequest(OrderNewRequest orderRequest, Map<String, String> configMap, String requestContent) {
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(orderRequest.getOrderInfo());
        GenerateOrderResponse response = new GenerateOrderResponse();
        try {
            HttpHeaders headers = new HttpHeaders();
            String baseUrl = buildBaseUrl(configMap, requestContent, CREATE_ORDER_METHOD, yanWenBaseUrl + CREATE_ORDER);
            headers.add("Content-Type", MediaType.APPLICATION_JSON_UTF8_VALUE);
            HttpEntity<String> entity = new HttpEntity<>(requestContent, headers);
            ResponseEntity<YanWenCreateOrderResponse> responseEntity = this.restTemplate.exchange(baseUrl, HttpMethod.POST, entity, YanWenCreateOrderResponse.class);
            if (responseEntity.getBody() != null && responseEntity.getBody().getSuccess()) {
                SuccessOrderResponse resp = JsonMapper.fromJson(JsonMapper.toJson(responseEntity.getBody().getData()), SuccessOrderResponse.class);
                String osLabelUrl = downloadLabelPdfAndUploadOSS(resp, configMap);
                GenerateOrderResponse.SuccessEntity successEntity = buildSuccessEntity(orderRequest.getOrderInfo(), resp.getWaybillNumber(), osLabelUrl, resp.getYanwenNumber(), resp.getYanwenOrderNumber());
                response.setSuccessEntity(successEntity);
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, requestContent, JsonMapper.toJson(responseEntity), successEntity.getLogisticsNo());
            } else {
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, requestContent, JsonMapper.toJson(responseEntity), null);
                response.setError(buildError("400", judgeResponse(responseEntity)));
            }
        } catch (Exception e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, requestContent, e.getMessage(), null);
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return response;
    }

    private String judgeResponse(ResponseEntity<YanWenCreateOrderResponse> resp) {
        if (resp.getBody() == null) {
            return JsonMapper.toJson(resp);
        }
        if (!resp.getBody().getSuccess() && StringUtils.hasText(resp.getBody().getMessage())) {
            return JsonMapper.toJson(resp.getBody());
        }
        return JsonMapper.toJson(resp);
    }

    private String buildBaseUrl(Map<String, String> configMap, String requestContent, String createOrderMethod, String url) {
        Date date = new Date();
        List<String> paramList = new ArrayList<>();
        paramList.add(configMap.get(API_TOKEN));
        paramList.add(configMap.get(USER_ID));
        paramList.add(requestContent);
        paramList.add("json");
        paramList.add(createOrderMethod);
        paramList.add(String.valueOf(date.getTime()));
        paramList.add("V1.0");
        paramList.add(configMap.get(API_TOKEN));
        String signContent = String.join("", paramList);
        String crypt = MD5Util.crypt(signContent).toLowerCase(Locale.ROOT);
        String finalUrl = url + "?user_id=" + configMap.get(USER_ID);
        finalUrl = finalUrl + "&method=" + createOrderMethod;
        finalUrl = finalUrl + "&format=json";
        finalUrl = finalUrl + "&timestamp=" + date.getTime();
        finalUrl = finalUrl + "&sign=" + crypt;
        finalUrl = finalUrl + "&version=V1.0";
        return finalUrl;
    }

    private String downloadLabelPdfAndUploadOSS(SuccessOrderResponse data, Map<String, String> configMap) throws IOException {
        GetLabelRequest getLabelRequest = new GetLabelRequest();
        getLabelRequest.setWaybillNumber(data.getWaybillNumber());
        String requestContent = JsonMapper.toJson(getLabelRequest);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", MediaType.APPLICATION_JSON_UTF8_VALUE);
        String baseUrl = buildBaseUrl(configMap, requestContent, GET_LABEL_METHOD, yanWenBaseUrl + CREATE_ORDER);
        HttpEntity<String> entity = new HttpEntity<>(requestContent, headers);
        ResponseEntity<YanWenCreateOrderResponse> responseEntity = this.restTemplate.exchange(baseUrl, HttpMethod.POST, entity, YanWenCreateOrderResponse.class);
        if (responseEntity.getBody() == null || !responseEntity.getBody().getSuccess()) {
            throw new BusinessServiceException("获取面单错误：" + JsonMapper.toJson(responseEntity));
        }
        GetLabelResponse resp = JsonMapper.fromJson(JsonMapper.toJson(responseEntity.getBody().getData()), GetLabelResponse.class);
        byte[] pdfByte = Base64.decode(resp.getBase64String());
        String labelFileName = String.format(LABEL_NAME, labelFolder, data.getWaybillNumber());
        return getPdfLabel(pdfByte, labelFileName);
    }


    @Override
    public void validateOrderRequest(OrderNewRequest request) {
        OrderNewInfo orderInfo = request.getOrderInfo();
        super.validateOrderRequest(request);
        Validator.isValid(orderInfo.getReceiver(), attr -> !Objects.isNull(attr), "receiver 节点不能为空");
        Validator.isValid(orderInfo.getReceiver().getPostCode(), StringUtils::hasText, "receiver.postCode 节点不能为空");
        Validator.isValid(orderInfo.getReceiver().getStreet(), attr -> attr.length() < 200, "地址长度超过限制");
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.labelFolder += "YanWen/";
        this.ossLabelFolder += "YanWen/";
    }

    @Override
    protected String buildSerializedRequest(OrderNewRequest request, TmsLogisticsAccountEntity logisticsAccountEntity) {
        Map<String, String> configMap = buildAccountConfig(logisticsAccountEntity, request.getOrderInfo());
        YanWenRequest request1 = buildLogisticsOrderRequest(request, configMap);
        return JsonMapper.toJson(request1);
    }





}
