package com.nsy.api.tms.external.wms.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 海外仓订单响应
 *
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel("海外仓订单响应")
public class OverseasWarehouseOrderResponse {

    @ApiModelProperty(value = "是否成功")
    private Boolean success;

    @ApiModelProperty(value = "响应消息")
    private String message;

    @ApiModelProperty(value = "响应代码")
    private String code;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
