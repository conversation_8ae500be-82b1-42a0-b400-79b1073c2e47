package com.nsy.api.tms.external.wms.response;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import io.swagger.annotations.ApiModel;

/**
 * 仓库实体响应类
 *
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "BdSpace", description = "仓库实体")
public class BdSpace {
    /**
     * 仓库id
     */
    private Integer spaceId;

    /**
     * 地区
     */
    private String location;

    private String locationStr;

    /**
     * 仓库名称
     */
    private String spaceName;

    /**
     * 仓库编号
     */
    private String spaceCode;

    /**
     * 仓库类型
     */
    private String spaceType;

    private String spaceTypeStr;

    /**
     * 库位数量
     */
    private Integer positionNum;

    /**
     * 国家
     */
    private String country;

    /**
     * 联系人
     */
    private String receiver;

    /**
     * 联系人电话
     */
    private String receiverMobile;

    /**
     * 联系人手机
     */
    private String receiverPhone;

    /**
     * 联系人邮箱
     */
    private String receiverEmail;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 县
     */
    private String county;

    /**
     * 仓库地址
     */
    private String address;

    private String zip;

    /**
     * 默认发货仓 1-是，0-否
     */
    private Integer isShipping;

    /**
     * 说明
     */
    private String description;

    /**
     * 部门
     */
    private String departments;

    /**
     * 是否已删除 1-是，0-否
     */
    private Integer isDeleted;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新者
     */
    private String updateBy;

    private Boolean isThirdPartySpace;

    private List<Integer> channelIdList = new ArrayList<>();

    public List<Integer> getChannelIdList() {
        return channelIdList;
    }

    public void setChannelIdList(List<Integer> channelIdList) {
        this.channelIdList = channelIdList;
    }

    public String getSpaceTypeStr() {
        return spaceTypeStr;
    }

    public void setSpaceTypeStr(String spaceTypeStr) {
        this.spaceTypeStr = spaceTypeStr;
    }

    public String getLocation() {
        return this.location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public String getLocationStr() {
        return locationStr;
    }

    public void setLocationStr(String locationStr) {
        this.locationStr = locationStr;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getSpaceCode() {
        return spaceCode;
    }

    public void setSpaceCode(String spaceCode) {
        this.spaceCode = spaceCode;
    }

    public String getSpaceType() {
        return spaceType;
    }

    public void setSpaceType(String spaceType) {
        this.spaceType = spaceType;
    }

    public Integer getPositionNum() {
        return this.positionNum;
    }

    public void setPositionNum(Integer positionNum) {
        this.positionNum = positionNum;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getIsShipping() {
        return isShipping;
    }

    public void setIsShipping(Integer isShipping) {
        this.isShipping = isShipping;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getVersion() {
        return this.version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return this.updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getZip() {
        return zip;
    }

    public void setZip(String zip) {
        this.zip = zip;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getReceiverMobile() {
        return receiverMobile;
    }

    public void setReceiverMobile(String receiverMobile) {
        this.receiverMobile = receiverMobile;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    public String getReceiverEmail() {
        return receiverEmail;
    }

    public void setReceiverEmail(String receiverEmail) {
        this.receiverEmail = receiverEmail;
    }

    public String getDepartments() {
        return departments;
    }

    public void setDepartments(String departments) {
        this.departments = departments;
    }

    public Boolean getIsThirdPartySpace() {
        return isThirdPartySpace;
    }

    public void setIsThirdPartySpace(Boolean isThirdPartySpace) {
        this.isThirdPartySpace = isThirdPartySpace;
    }
}